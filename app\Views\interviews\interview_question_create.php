<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-plus mr-2"></i>
                        Add Interview Question
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/open/' . $position['id']) ?>">Open Interview</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/questions/' . $positionId) ?>">Questions</a></li>
                        <li class="breadcrumb-item active">Add Question</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position: <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/questions/' . $positionId) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Questions
                        </a>
                    </div>
                </div>
            </div>

            <!-- Add Question Form -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-question-circle mr-2"></i>
                        New Interview Question
                    </h3>
                </div>
                <form method="POST" action="<?= base_url('interviews/questions/store/' . $positionId) ?>">
                    <?= csrf_field() ?>
                    <div class="card-body">
                        <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="question_text">Question Text <span class="text-danger">*</span></label>
                                    <textarea class="form-control" 
                                              id="question_text" 
                                              name="question_text" 
                                              rows="4" 
                                              placeholder="Enter the interview question..."
                                              required><?= old('question_text') ?></textarea>
                                    <small class="form-text text-muted">
                                        Write a clear and specific question that will help evaluate the candidate's suitability for this position.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="set_score">Maximum Score <span class="text-danger">*</span></label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="set_score" 
                                           name="set_score" 
                                           min="1" 
                                           max="100" 
                                           value="<?= old('set_score', 10) ?>" 
                                           required>
                                    <small class="form-text text-muted">
                                        Set the maximum points that can be awarded for this question (1-100).
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Question Preview</label>
                                    <div class="alert alert-light">
                                        <strong>This question will be automatically numbered</strong><br>
                                        <small class="text-muted">
                                            The system will assign the next available question number when you save this question.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sample Questions for Reference -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card card-light">
                                    <div class="card-header">
                                        <h6 class="card-title">
                                            <i class="fas fa-lightbulb mr-1"></i>
                                            Sample Interview Questions
                                        </h6>
                                        <div class="card-tools">
                                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>General Questions:</h6>
                                                <ul class="list-unstyled">
                                                    <li><small>• Tell us about yourself and your background</small></li>
                                                    <li><small>• Why are you interested in this position?</small></li>
                                                    <li><small>• What are your greatest strengths?</small></li>
                                                    <li><small>• Describe a challenging situation you faced and how you handled it</small></li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Technical Questions:</h6>
                                                <ul class="list-unstyled">
                                                    <li><small>• Describe your experience with [specific skill/technology]</small></li>
                                                    <li><small>• How would you approach [specific work scenario]?</small></li>
                                                    <li><small>• What tools or methods do you use for [specific task]?</small></li>
                                                    <li><small>• How do you stay updated with industry trends?</small></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save mr-1"></i>
                                    Save Question
                                </button>
                                <a href="<?= base_url('interviews/questions/' . $positionId) ?>" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times mr-1"></i>
                                    Cancel
                                </a>
                            </div>
                            <div class="col-md-6 text-right">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    All fields marked with <span class="text-danger">*</span> are required
                                </small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Auto-resize textarea
    $('#question_text').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Form validation
    $('form').on('submit', function(e) {
        var questionText = $('#question_text').val().trim();
        var setScore = $('#set_score').val();

        if (questionText.length < 10) {
            e.preventDefault();
            alert('Question text must be at least 10 characters long.');
            $('#question_text').focus();
            return false;
        }

        if (setScore < 1 || setScore > 100) {
            e.preventDefault();
            alert('Maximum score must be between 1 and 100.');
            $('#set_score').focus();
            return false;
        }
    });

    // Sample question click to fill form
    $('.list-unstyled li').on('click', function() {
        var sampleText = $(this).text().replace('• ', '');
        $('#question_text').val(sampleText);
        $('#question_text').trigger('input'); // Trigger auto-resize
    });
});
</script>
<?= $this->endSection() ?>
