<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Position Groups</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <!-- Position Groups Card -->
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-layer-group mr-2"></i>
                                Position Groups
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Position Groups Table Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h4 class="mb-0">
                                            <i class="fas fa-list mr-2"></i>
                                            Groups List
                                        </h4>
                                        <a href="<?= site_url('positions/new') ?>" class="btn btn-primary">
                                            <i class="fas fa-plus mr-1"></i>
                                            Create Group
                                        </a>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th width="5%">Priority</th>
                                                    <th width="20%">Group Name</th>
                                                    <th width="35%">Description</th>
                                                    <th width="15%">Total Positions</th>
                                                    <th width="25%">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($groups as $group): ?>
                                                <tr>
                                                    <td class="align-middle text-center"><?= esc($group['priority']) ?></td>
                                                    <td class="align-middle"><?= esc($group['group_name']) ?></td>
                                                    <td class="align-middle"><?= esc($group['description']) ?></td>
                                                    <td class="align-middle text-center">
                                                        <span class="">
                                                            <?= isset($group['total_positions']) ? $group['total_positions'] : '0' ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="<?= site_url('positions/show/' . $group['id']) ?>"
                                                               class="btn btn-info btn-sm"
                                                               title="View Positions">
                                                               View Positions <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="<?= site_url('positions/edit/' . $group['id']) ?>"
                                                               class="btn btn-warning btn-sm"
                                                               title="Edit Group">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<?= site_url('positions/delete/' . $group['id']) ?>"
                                                               class="btn btn-danger btn-sm"
                                                               onclick="return confirm('Are you sure you want to delete the group \'<?= esc($group['group_name']) ?>\'?')"
                                                               title="Delete Group">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
