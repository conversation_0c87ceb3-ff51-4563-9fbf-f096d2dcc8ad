<?php

namespace App\Controllers;

use App\Models\PositionModel;
use App\Models\DakoiiOrgModel;
use App\Models\PositionsGroupModel;

class Positions extends BaseController
{
    protected $positionModel;
    protected $positionGroupModel;
    protected $orgModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->positionModel = new PositionModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->orgModel = new DakoiiOrgModel();
        $this->session = session();
    }

    /**
     * Display a listing of position groups
     *
     * @return mixed
     */
    public function index()
    {
        // Get groups with position counts
        $groups = $this->positionGroupModel->select('positions_groups.*, COUNT(positions.id) as total_positions')
                            ->join('positions', 'positions.position_group_id = positions_groups.id', 'left')
                            ->where('positions_groups.org_id', session('org_id'))
                            ->groupBy('positions_groups.id')
                            ->orderBy('positions_groups.priority', 'ASC')
                            ->orderBy('positions_groups.group_name', 'ASC')
                            ->findAll();

        $data = [
            'title' => 'Position Groups',
            'menu' => 'positions',
            'groups' => $groups
        ];

        return view('positions/positions_index', $data);
    }

    /**
     * Show the form for creating a new position group
     *
     * @return mixed
     */
    public function new()
    {
        $data = [
            'title' => 'Create Position Group',
            'menu' => 'positions'
        ];

        return view('positions/positions_new', $data);
    }

    /**
     * Store a newly created position group
     *
     * @return mixed
     */
    public function create()
    {
        $rules = [
            'group_name' => 'required|min_length[3]|max_length[255]',
            'description' => 'required',
            'priority' => 'required|numeric'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()
                ->withInput()
                ->with('validation', $this->validator);
        }

        $this->positionGroupModel->save([
            'org_id' => session('org_id'),
            'group_name' => $this->request->getVar('group_name'),
            'description' => $this->request->getVar('description'),
            'priority' => $this->request->getVar('priority'),
            'created_by' => session('user_id'),
            'updated_by' => session('user_id')
        ]);

        return redirect()->to(site_url('positions'))->with('success', 'Position group created successfully.');
    }

    /**
     * Display the specified position group
     *
     * @param int $id
     * @return mixed
     */
    public function show($id)
    {
        $group = $this->positionGroupModel->where('org_id', session('org_id'))->find($id);

        if (empty($group)) {
            return redirect()->to(site_url('positions'))->with('error', 'Position group not found.');
        }

        $data = [
            'title' => 'View Position Group',
            'menu' => 'positions',
            'group' => $group,
            'positions' => $this->positionModel->where('position_group_id', $id)
                                               ->where('org_id', session('org_id'))
                                               ->orderBy('position_no', 'ASC')
                                               ->findAll()
        ];

        return view('positions/positions_show', $data);
    }

    /**
     * Show the form for editing the specified position group
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id)
    {
        $group = $this->positionGroupModel->find($id);

        if (empty($group)) {
            return redirect()->to(site_url('positions'))->with('error', 'Position group not found.');
        }

        $data = [
            'title' => 'Edit Position Group',
            'menu' => 'positions',
            'group' => $group
        ];

        return view('positions/positions_edit', $data);
    }

    /**
     * Update the specified position group
     *
     * @param int $id
     * @return mixed
     */
    public function update($id)
    {
        $group = $this->positionGroupModel->find($id);

        if (empty($group)) {
            return redirect()->to(site_url('positions'))->with('error', 'Position group not found.');
        }

        $rules = [
            'group_name' => 'required|min_length[3]|max_length[255]',
            'description' => 'required',
            'priority' => 'required|numeric'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()
                ->withInput()
                ->with('validation', $this->validator);
        }

        $this->positionGroupModel->update($id, [
            'group_name' => $this->request->getVar('group_name'),
            'description' => $this->request->getVar('description'),
            'priority' => $this->request->getVar('priority'),
            'updated_by' => session('user_id')
        ]);

        return redirect()->to(site_url('positions'))->with('success', 'Position group updated successfully.');
    }

    /**
     * Remove the specified position group
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id)
    {
        $group = $this->positionGroupModel->find($id);

        if (empty($group)) {
            return redirect()->to(site_url('positions'))->with('error', 'Position group not found.');
        }

        // Check if there are any positions in this group
        $positions = $this->positionModel->where('position_group_id', $id)->findAll();

        if (!empty($positions)) {
            return redirect()->to(site_url('positions'))->with('error', 'Cannot delete group. There are positions associated with this group.');
        }

        $this->positionGroupModel->delete($id);

        return redirect()->to(site_url('positions'))->with('success', 'Position group deleted successfully.');
    }

    /**
     * Show the form for creating a new position
     *
     * @param int $group_id
     * @return mixed
     */
    public function newPosition($group_id)
    {
        $group = $this->positionGroupModel->find($group_id);
        if (!$group) {
            return redirect()->to('positions')->with('error', 'Position group not found');
        }

        $data = [
            'title' => "Create Position",
            'menu' => "positions",
            'group_id' => $group_id,
            'group' => $group
        ];

        return view('positions/positions_new_position', $data);
    }

    /**
     * Store a newly created position
     *
     * @return mixed
     */
    public function createPosition()
    {
        $group_id = $this->request->getPost('position_group_id');

        $group = $this->positionGroupModel->find($group_id);
        if (!$group) {
            return redirect()->to('positions')->with('error', 'Position group not found');
        }

        //find existing position no
        $existingPosition = $this->positionModel->where('position_no', $this->request->getPost('position_no'))
            ->where('org_id', session('org_id'))
            ->first();

        if ($existingPosition) {
            return redirect()->back()->withInput()->with('error', 'Position number already exists');
        }

        $data = [
            'position_no' => $this->request->getPost('position_no'),
            'designation' => $this->request->getPost('designation'),
            'classification' => $this->request->getPost('classification'),
            'award' => $this->request->getPost('award'),
            'qualifications' => $this->request->getPost('qualifications'),
            'knowledge' => $this->request->getPost('knowledge'),
            'skills_competencies' => $this->request->getPost('skills_competencies'),
            'job_experiences' => $this->request->getPost('job_experiences'),
            'org_id' => session('org_id'),
            'position_group_id' => $group_id,
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'created_by' => $this->session->get('user_id'),
            'updated_by' => $this->session->get('user_id')
        ];

        if ($this->positionModel->insert($data)) {
            return redirect()->to("positions/show/$group_id")->with('success', 'Position created successfully');
        } else {
            return redirect()->back()->withInput()->with('errors', $this->positionModel->errors());
        }
    }

    /**
     * Show the form for editing the specified position
     *
     * @param int $id
     * @return mixed
     */
    public function editPosition($id)
    {
        $position = $this->positionModel->find($id);
        if (!$position) {
            return redirect()->to('positions')->with('error', 'Position not found');
        }

        // Get the group information
        $group = $this->positionGroupModel->find($position['position_group_id']);
        if (!$group) {
            return redirect()->to('positions')->with('error', 'Position group not found');
        }

        $data = [
            'title' => "Edit Position",
            'menu' => "positions",
            'position' => $position,
            'group' => $group
        ];

        return view('positions/positions_edit_position', $data);
    }

    /**
     * Update the specified position
     *
     * @param int $id
     * @return mixed
     */
    public function updatePosition($id)
    {
        $position = $this->positionModel->find($id);
        if (!$position) {
            return redirect()->to('positions')->with('error', 'Position not found');
        }

        $group_id = $position['position_group_id'];

        $data = [
            'position_no' => $this->request->getPost('position_no'),
            'designation' => $this->request->getPost('designation'),
            'classification' => $this->request->getPost('classification'),
            'award' => $this->request->getPost('award'),
            'qualifications' => $this->request->getPost('qualifications'),
            'knowledge' => $this->request->getPost('knowledge'),
            'skills_competencies' => $this->request->getPost('skills_competencies'),
            'job_experiences' => $this->request->getPost('job_experiences'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'for_interview' => $this->request->getPost('for_interview') ? 1 : 0,
            'updated_by' => $this->session->get('user_id')
        ];

        if ($this->positionModel->update($id, $data)) {
            return redirect()->to("positions/show/{$group_id}")
                           ->with('success', 'Position updated successfully');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->positionModel->errors());
        }
    }

    /**
     * Remove the specified position
     *
     * @param int $id
     * @return mixed
     */
    public function deletePosition($id)
    {
        $position = $this->positionModel->find($id);
        if (!$position) {
            return redirect()->to('positions')->with('error', 'Position not found');
        }

        $group_id = $position['position_group_id'];

        if ($this->positionModel->delete($id)) {
            return redirect()->to("positions/show/{$group_id}")->with('success', 'Position deleted successfully');
        } else {
            return redirect()->back()->with('error', 'Failed to delete position');
        }
    }
}
