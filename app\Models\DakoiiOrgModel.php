<?php

namespace App\Models;

use CodeIgniter\Model;

class DakoiiOrgModel extends Model
{
    protected $table      = 'dakoii_org';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'orgcode', 
        'name', 
        'description', 
        'advertisement_no',    // New field
        'advertisement_date',  // New field
        'mode_of_advert',      // New field
        'org_logo',
        'sort_out_of',
        'introduction',
        'composition',
        'criteria',
        'culling',
        'ai_model', //openai, anthropic, gemini, deepseek,
        'interview_settings', //json-interview minutes/per/interviewee, transition_time, start_time, end_time, start_date, break_times, break_days
        'is_active', 
        'license_status'

    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;

    // You can add custom methods here if needed
}
