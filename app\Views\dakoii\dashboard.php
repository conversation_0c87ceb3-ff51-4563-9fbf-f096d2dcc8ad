<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<body>
    <div class="container-fluid p-2">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="float-left">Dakoii Dashboard</h4>
                    </div>
                    <div class="card-body bg-light p-0">
                        <div class="row p-3">
                            <div class="col-md-4">
                                <h5>Dakoii Users</h5>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Name</th>
                                            <th>Username</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($dusers as $index => $user): ?>
                                            <tr>
                                                <td><?= $index + 1 ?></td>
                                                <td><?= esc($user['name']) ?></td>
                                                <td><?= esc($user['username']) ?></td>
                                                <td><?= esc($user['role']) ?></td>
                                                <td><?= $user['is_active'] ? 'Active' : 'Inactive' ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="col-md-8 mt-4">
                                <h5>Organizations</h5>
                                <button type="button" class="btn btn-primary mb-3" data-toggle="modal"
                                    data-target="#createOrgModal">
                                    Create New Organization
                                </button>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Logo</th>
                                            <th>#</th>
                                            <th>Org Code</th>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Status</th>
                                            <th>License Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($organizations as $index => $org): ?>
                                            <tr>
                                                <td>

                                                    <img src="<?= imgcheck($org['org_logo']) ?>" alt=" Logo"
                                                        style="width: 50px; height: 50px; object-fit: cover;">

                                                </td>
                                                <td><?= $index + 1 ?></td>
                                                <td><?= esc($org['orgcode']) ?></td>
                                                <td><?= esc($org['name']) ?></td>
                                                <td><?= esc($org['description']) ?></td>
                                                <td><?= $org['is_active'] ? 'Active' : 'Inactive' ?></td>
                                                <td><?= esc($org['license_status']) ?></td>
                                                <td>
                                                    <a href="<?= base_url('dakoii/viewOrg/' . $org['id']) ?>"
                                                        class="btn btn-sm btn-info">View</a>
                                                    <button type="button" class="btn btn-sm btn-primary" data-toggle="modal"
                                                        data-target="#editOrgModal<?= $org['id'] ?>">
                                                        Edit
                                                    </button>
                                                    <a href="<?= base_url('dakoii/deleteOrg/' . $org['id']) ?>"
                                                        class="btn btn-sm btn-danger"
                                                        onclick="return confirm('Are you sure you want to delete this organization?')">Delete</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Organization Modal -->
    <div class="modal fade" id="createOrgModal" tabindex="-1" role="dialog" aria-labelledby="createOrgModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createOrgModalLabel">Create New Organization</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="<?= base_url('dakoii/createOrg') ?>" method="post" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="orgcode">Org Code</label>
                            <input type="text" class="form-control" id="orgcode" name="orgcode" required>
                        </div>
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="org_logo">Organization Logo</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="org_logo" name="org_logo"
                                    accept="image/*">
                                <label class="custom-file-label" for="org_logo">Choose file</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="ai_model">AI Model</label>
                            <select class="form-control" id="ai_model" name="ai_model" required>
                                <option value="anthropic">Anthropic</option>
                                <option value="gemini">Gemini</option>
                                <option value="deepseek">DeepSeek</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="is_active">Active</label>
                            <input type="checkbox" id="is_active" name="is_active" value="1">
                        </div>
                        <div class="form-group">
                            <label for="license_status">License Status</label>
                            <input type="text" class="form-control" id="license_status" name="license_status" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Create Organization</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Organization Modals -->
    <?php foreach ($organizations as $org): ?>
        <div class="modal fade" id="editOrgModal<?= $org['id'] ?>" tabindex="-1" role="dialog"
            aria-labelledby="editOrgModalLabel<?= $org['id'] ?>" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editOrgModalLabel<?= $org['id'] ?>">Edit Organization</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form action="<?= base_url('dakoii/updateOrg/' . $org['id']) ?>" method="post"
                        enctype="multipart/form-data">
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="orgcode<?= $org['id'] ?>">Org Code</label>
                                <input type="text" class="form-control" id="orgcode<?= $org['id'] ?>" name="orgcode"
                                    value="<?= esc($org['orgcode']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="name<?= $org['id'] ?>">Name</label>
                                <input type="text" class="form-control" id="name<?= $org['id'] ?>" name="name"
                                    value="<?= esc($org['name']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="description<?= $org['id'] ?>">Description</label>
                                <textarea class="form-control" id="description<?= $org['id'] ?>" name="description"
                                    required><?= esc($org['description']) ?></textarea>
                            </div>
                            <div class="form-group">
                                <label for="org_logo<?= $org['id'] ?>">Organization Logo</label>
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="org_logo<?= $org['id'] ?>"
                                        name="org_logo" accept="image/*">
                                    <label class="custom-file-label" for="org_logo<?= $org['id'] ?>">Choose file</label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="ai_model<?= $org['id'] ?>">AI Model</label>
                                <select class="form-control" id="ai_model<?= $org['id'] ?>" name="ai_model" required>
                                    <option value="anthropic" <?= ($org['ai_model'] == 'anthropic') ? 'selected' : '' ?>>
                                        Anthropic</option>
                                    <option value="gemini" <?= ($org['ai_model'] == 'gemini') ? 'selected' : '' ?>>Gemini
                                    </option>
                                    <option value="deepseek" <?= ($org['ai_model'] == 'deepseek') ? 'selected' : '' ?>>DeepSeek
                                    </option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="is_active<?= $org['id'] ?>">Active</label>
                                <input type="checkbox" id="is_active<?= $org['id'] ?>" name="is_active" value="1"
                                    <?= $org['is_active'] ? 'checked' : '' ?>>
                            </div>
                            <div class="form-group">
                                <label for="license_status<?= $org['id'] ?>">License Status</label>
                                <input type="text" class="form-control" id="license_status<?= $org['id'] ?>"
                                    name="license_status" value="<?= esc($org['license_status']) ?>" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary">Update Organization</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>

    <script>
        // Initialize custom file input
        $(document).ready(function () {
            // Add the following code if you want the name of the file appear on select
            $(".custom-file-input").on("change", function () {
                var fileName = $(this).val().split("\\").pop();
                $(this).siblings(".custom-file-label").addClass("selected").html(fileName);
            });
        });
    </script>
</body>

<?= $this->endSection() ?>