<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-chart-bar mr-2"></i>
                        Interview Report
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interview-reports/index/' . $positionGroup['id']) ?>">Interview Reports</a></li>
                        <li class="breadcrumb-item active">Position Report</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position Information
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interview-reports/index/' . $positionGroup['id']) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i> Back to Positions
                        </a>
                        <button onclick="window.print()" class="btn btn-info btn-sm">
                            <i class="fas fa-print mr-1"></i> Print Report
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Position No:</strong></td>
                                    <td><?= esc($position['position_no']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Designation:</strong></td>
                                    <td><?= esc($position['designation']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Classification:</strong></td>
                                    <td><?= esc($position['classification']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Award:</strong></td>
                                    <td><?= esc($position['award']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Group:</strong></td>
                                    <td><?= esc($positionGroup['group_name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Possible Score:</strong></td>
                                    <td><span class="badge badge-primary"><?= number_format($totalPossibleScore, 1) ?></span> per interviewer</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interview Statistics Card -->
            <div class="card card-info card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Interview Statistics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Interviewees</span>
                                    <span class="info-box-number"><?= count($applicants) ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description">Shortlisted</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-question-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Questions</span>
                                    <span class="info-box-number"><?= count($questions) ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description">Total</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-user-tie"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Interviewers</span>
                                    <span class="info-box-number"><?= count($interviewers) ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description">Panel Members</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="info-box bg-primary">
                                <span class="info-box-icon"><i class="fas fa-star"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Max Score</span>
                                    <span class="info-box-number"><?= number_format($totalPossibleScore * count($interviewers), 1) ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description">Per Applicant</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interview Results Table -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-table mr-2"></i>
                        Interview Results
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-success">
                            <?= count($applicants) ?> Interviewees
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($applicants)): ?>
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> No Interviewees</h5>
                            <p>There are no shortlisted applicants for this position.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive" id="interview-results-table">
                            <table class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th width="5%">Rank</th>
                                        <th width="20%">Interviewee Name</th>
                                        <?php foreach ($interviewers as $interviewer): ?>
                                            <th width="<?= floor(40 / count($interviewers)) ?>%" class="text-center">
                                                <?= esc($interviewer['interviewer_name']) ?><br>
                                                <small class="text-white-50"><?= esc($interviewer['interviewer_position']) ?></small>
                                            </th>
                                        <?php endforeach; ?>
                                        <th width="10%" class="text-center">Total Score</th>
                                        <th width="10%" class="text-center">Total Out Of</th>
                                        <th width="10%" class="text-center">Average</th>
                                        <th width="10%" class="text-center">Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applicants as $index => $applicant): ?>
                                        <tr>
                                            <td class="align-middle text-center"><strong><?= $index + 1 ?></strong></td>
                                            <td class="align-middle">
                                                <strong><?= esc($applicant['name']) ?></strong>
                                            </td>
                                            <?php foreach ($interviewers as $interviewer): ?>
                                                <td class="align-middle text-center">
                                                    <?php if (isset($applicant['interviewer_totals'][$interviewer['id']])): ?>
                                                        <?= number_format($applicant['interviewer_totals'][$interviewer['id']]['total'], 1) ?>
                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>
                                                </td>
                                            <?php endforeach; ?>
                                            <td class="align-middle text-center">
                                                <strong><?= number_format($applicant['grand_total'], 1) ?></strong>
                                            </td>
                                            <td class="align-middle text-center">
                                                <?= number_format($applicant['max_possible_total'], 1) ?>
                                            </td>
                                            <td class="align-middle text-center">
                                                <?= number_format($applicant['average_score'], 1) ?>
                                            </td>
                                            <td class="align-middle text-center">
                                                <?= number_format($applicant['percentage'], 1) ?>%
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Interview Panel Information -->
            <div class="card card-warning card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users mr-2"></i>
                        Interview Panel
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($interviewers as $interviewer): ?>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h5 class="card-title"><?= esc($interviewer['interviewer_name']) ?></h5>
                                        <p class="card-text text-muted"><?= esc($interviewer['interviewer_position']) ?></p>
                                        <small class="text-muted">Panel Member</small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize DataTable for results
    $('#interview-results-table table').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": false, // Show all results
        "order": [[ 0, "asc" ]], // Sort by rank ascending (already sorted by total score in controller)
        "columnDefs": [
            { "orderable": false, "targets": [0] } // Disable sorting for rank column to maintain ranking
        ]
    });
});

// Print functionality
function printReport() {
    window.print();
}

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('print-mode');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('print-mode');
});
</script>

<style>
@media print {
    .content-wrapper {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        margin-bottom: 20px !important;
    }
    
    .btn, .breadcrumb, .card-tools {
        display: none !important;
    }
    
    .table {
        font-size: 12px !important;
    }
    
    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background-color: #fff !important;
    }
}
</style>
<?= $this->endSection() ?>
