<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Eliminated Applicants - <?= esc($positionName) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("reports/viewPositions/{$groupId}") ?>"><?= esc($groupName) ?></a></li>
                        <li class="breadcrumb-item active">Eliminated Applicants</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Eliminated Applicants for <?= esc($groupName) ?></h3>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Position No</th>
                                        <th>Position</th>
                                        <th>Reason for Elimination</th>
                                        
                                        
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($eliminatedApplicants as $applicant): ?>
                                    <tr>
                                        <td><?= esc($applicant['name']) ?></td>
                                        <td><?= esc($applicant['position_no']) ?></td>
                                        <td><?= esc($applicant['position']) ?></td>
                                        
                                        <td><?= esc($applicant['app_status_reason']) ?></td>
                                        
                                        
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
