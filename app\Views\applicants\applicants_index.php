<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-briefcase mr-2"></i>
                        <?= $title ?>
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('applicants/positionGroups') ?>">Position Groups</a></li>
                        <li class="breadcrumb-item active"><?= esc($group['group_name']) ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-briefcase mr-2"></i>
                                Positions in <?= esc($group['group_name']) ?>
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped text-nowrap">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Position No.</th>
                                            <th>Designation</th>
                                            <th>Classification</th>
                                            <th>Status</th>
                                            <th>No. of Applicants</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($positions as $position): ?>
                                        <tr>
                                            <td>(<?= esc($position['position_no']) ?>)</td>
                                            <td><?= esc($position['designation']) ?></td>
                                            <td><?= esc($position['classification']) ?></td>
                                            <td>
                                                <?php if ($position['is_active']): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?= number_format($position['applicant_count']) ?>
                                            </td>
                                            <td>
                                                <a href="<?= site_url('applicants/list/' . $position['id']) ?>" 
                                                   class="btn btn-primary btn-sm">
                                                    View Applicants <i class="fas fa-eye ml-1"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.badge {
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
}
</style>

<?= $this->endSection() ?>
