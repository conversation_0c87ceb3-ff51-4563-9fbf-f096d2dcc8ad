<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Applicants by Rank - <?= esc($positionName) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("reports/viewPositions/{$groupId}") ?>"><?= esc($groupName) ?></a></li>
                        <li class="breadcrumb-item active">Applicants by Rank</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="text-center">
                                <img src="<?= imgcheck($org['org_logo']) ?>" alt="Organization Logo" class="img-fluid" style="align-items: center; max-width: 50px; display: block; margin: 0 auto;">
                            </div>
                            <h3 class="text-center"><?= session()->get('org_name') ?></h3>
                            <h4 class="text-center"><?= esc($groupName) ?></h4>
                            <h4 class="text-center"> <?= esc($positionNo) ?> - <?= esc($positionName) ?></h4>


                            <h3 class="card-title text-center">Ranked Applicants </h3>
                            
                                <div class="row d-flex justify-content-between no-print">
                                    <!-- Bulk actions column -->
                                    <!-- <div class="col-md-4">
                                        <div class="d-flex align-items-center">
                                            <div class="custom-control custom-checkbox mr-2">
                                                <input type="checkbox" class="custom-control-input" id="selectAll">
                                                <label class="custom-control-label" for="selectAll">Select All</label>
                                            </div>
                                        </div>
                                    </div> -->

                                    <!-- Bulk action dropdown with apply button -->
                                    <div class="col-md-3">
                                        <div class="input-group">
                                            <select id="bulkActionSelect" class="form-control">
                                                <option value="">Bulk Actions</option>
                                                <option value="shortlist">Shortlist Selected</option>
                                                <option value="eliminate">Eliminate Selected</option>
                                                <option value="withdraw">Withdraw Selected</option>
                                            </select>
                                            <div class="input-group-append">
                                                <button id="applyBulkAction" class="btn btn-primary">Apply</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Print options column -->
                                    <div class="col-md-3">
                                        <select id="noPrintDropdown" class="form-control" multiple>
                                            <!-- Options will be populated by JavaScript -->
                                        </select>
                                    </div>
                                    
                                    
                                    <div class="col-md-3">
                                        <select id="sortDropdown" class="form-control">
                                            <option value="">Sort by - All</option>
                                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                                <option value="<?= $i ?>"><?= $i ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select id="statusDropdown" class="form-control">
                                            <option value="All">Status - All</option>
                                            <option value="Shortlisted">Shortlisted</option>
                                            <option value="Eliminated">Eliminated</option>
                                            <option value="Withdrawn">Withdrawn</option>
                                        </select>
                                    </div>
                                
                                    
                                </div>

                                <script>
                                    document.addEventListener("DOMContentLoaded", function() {
                                        const tableHeaders = document.querySelectorAll("thead th");
                                        const dropdown = document.getElementById("noPrintDropdown");
                                        const tableRows = document.querySelectorAll("tbody tr");

                                        // Populate the dropdown with table headers
                                        tableHeaders.forEach((header, index) => {
                                            const option = document.createElement("option");
                                            option.value = index;
                                            option.textContent = header.textContent;
                                            option.selected = header.classList.contains("no-print");
                                            dropdown.appendChild(option);
                                        });

                                        // Function to toggle "no-print" class on entire column
                                        function toggleColumnPrint(columnIndex, shouldHide) {
                                            // Toggle class on the header
                                            if (shouldHide) {
                                                tableHeaders[columnIndex].classList.add("no-print");
                                            } else {
                                                tableHeaders[columnIndex].classList.remove("no-print");
                                            }

                                            // Toggle class on each cell in the column
                                            tableRows.forEach(row => {
                                                const cell = row.children[columnIndex];
                                                if (cell) {
                                                    if (shouldHide) {
                                                        cell.classList.add("no-print");
                                                    } else {
                                                        cell.classList.remove("no-print");
                                                    }
                                                }
                                            });
                                        }

                                        // Listen for changes in the dropdown to toggle the class on entire columns
                                        dropdown.addEventListener("change", function() {
                                            Array.from(dropdown.options).forEach((option, index) => {
                                                toggleColumnPrint(index, option.selected);
                                            });
                                        });
                                    });
                                </script>

                                <style>
                                    @media print {
                                        .no-print {
                                            display: none;
                                        }
                                    }
                                </style>

                                
                            

                            <script>
                                document.addEventListener("DOMContentLoaded", function() {
                                    const sortDropdown = document.getElementById("sortDropdown");
                                    const statusDropdown = document.getElementById("statusDropdown");
                                    const tableBody = document.querySelector("table tbody");
                                    const allRows = Array.from(tableBody.rows); // Store all table rows initially

                                    // Function to display the selected number of rows and filter by status
                                    function displayRows(count, status) {
                                        // First, hide all rows
                                        allRows.forEach(row => (row.style.display = "none"));

                                        // Then, show the selected number of rows that match the status
                                        let shownRows = 0;
                                        for (let i = 0; i < allRows.length && shownRows < count; i++) {
                                            const rowStatus = allRows[i].cells[5].textContent.trim(); // Assuming status is in the 6th column
                                            if (status === "All" || rowStatus === status) {
                                                allRows[i].style.display = "";
                                                shownRows++;
                                            }
                                        }
                                    }

                                    // Add event listener to the sort dropdown
                                    sortDropdown.addEventListener("change", function() {
                                        const selectedValue = parseInt(this.value, 10) || allRows.length;
                                        const selectedStatus = statusDropdown.value;
                                        displayRows(selectedValue, selectedStatus);
                                    });

                                    // Add event listener to the status dropdown
                                    statusDropdown.addEventListener("change", function() {
                                        const selectedValue = parseInt(sortDropdown.value, 10) || allRows.length;
                                        const selectedStatus = this.value;
                                        displayRows(selectedValue, selectedStatus);
                                    });

                                    // Initially display all rows
                                    displayRows(allRows.length, "All");
                                });
                            </script>

                        </div>
                        <div class="card-body">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th class="no-print">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="headerCheckbox">
                                                <label class="custom-control-label" for="headerCheckbox"></label>
                                            </div>
                                        </th>
                                        <th>Rank</th>
                                        <th>Name</th>
                                        <th>Position No.</th>
                                        <th>Position</th>
                                        <th>Total Rating</th>
                                        <th>Status</th>
                                        <th>Reason</th>
                                        <th class="no-print">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($rankedApplicants as $applicant): ?>
                                        <tr>
                                            <td class="no-print">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" class="custom-control-input applicant-checkbox" 
                                                           id="applicant<?= $applicant['id'] ?>" 
                                                           value="<?= $applicant['id'] ?>">
                                                    <label class="custom-control-label" for="applicant<?= $applicant['id'] ?>"></label>
                                                </div>
                                            </td>
                                            <td><?= esc($applicant['overall_rank']) ?></td>
                                            <td><?= esc($applicant['name']) ?></td>
                                            <td><?= esc($applicant['position_no']) ?></td>
                                            <td><?= esc($applicant['position']) ?></td>
                                            <td><?= esc($applicant['rate_total']) ?> / 61</td>
                                            <td id="statusCell<?= $applicant['id'] ?>"><?= esc($applicant['application_status']) ?></td>
                                            <td><?= esc($applicant['app_status_reason']) ?></td>
                                            <td class="no-print">


                                                <div class="btn-group">
                                                    <!-- Shortlist button -->
                                                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#shortlistModal<?= $applicant['id'] ?>">Shortlist</button>

                                                    <!-- Eliminate button -->
                                                    <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#eliminateModal<?= $applicant['id'] ?>">Eliminate</button>

                                                    <!-- Withdraw button -->
                                                    <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#withdrawModal<?= $applicant['id'] ?>">Withdraw</button>
                                                </div>

                                                <!-- Shortlist modal -->
                                                <div class="modal fade" id="shortlistModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="shortlistModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="shortlistModalLabel<?= $applicant['id'] ?>">Shortlist Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <form action="<?= base_url("applicants/shortlist/{$applicant['id']}") ?>" method="post">
                                                                    <div class="form-group">
                                                                        <label for="shortlistReason<?= $applicant['id'] ?>">Reason for Shortlisting</label>
                                                                        <textarea class="form-control" id="shortlistReason<?= $applicant['id'] ?>" name="shortlistReason" required></textarea>
                                                                    </div>
                                                                    <button type="submit" class="btn btn-primary">Shortlist</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Eliminate modal -->
                                                <div class="modal fade" id="eliminateModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="eliminateModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="eliminateModalLabel<?= $applicant['id'] ?>">Eliminate Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <form action="<?= base_url("applicants/eliminate/{$applicant['id']}") ?>" method="post">
                                                                    <div class="form-group">
                                                                        <label for="eliminateReason<?= $applicant['id'] ?>">Reason for Elimination</label>
                                                                        <textarea class="form-control" id="eliminateReason<?= $applicant['id'] ?>" name="eliminateReason" required></textarea>
                                                                    </div>
                                                                    <button type="submit" class="btn btn-danger">Eliminate</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Withdraw modal -->
                                                <div class="modal fade" id="withdrawModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="withdrawModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="withdrawModalLabel<?= $applicant['id'] ?>">Withdraw Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <form action="<?= base_url("applicants/withdraw/{$applicant['id']}") ?>" method="post">
                                                                    <div class="form-group">
                                                                        <label for="withdrawReason<?= $applicant['id'] ?>">Reason for Withdrawal</label>
                                                                        <textarea class="form-control" id="withdrawReason<?= $applicant['id'] ?>" name="withdrawReason" required></textarea>
                                                                    </div>
                                                                    <button type="submit" class="btn btn-warning">Withdraw</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Add Bulk Action Modals -->
<!-- Bulk Shortlist Modal -->
<div class="modal fade" id="bulkShortlistModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Shortlist Selected Applicants</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkShortlistForm">
                    <div class="form-group">
                        <label for="bulkShortlistReason">Reason for Shortlisting</label>
                        <textarea class="form-control" id="bulkShortlistReason" name="shortlistReason" required></textarea>
                    </div>
                    <input type="hidden" id="bulkShortlistIds" name="applicantIds">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmBulkShortlist">Shortlist</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Eliminate Modal -->
<div class="modal fade" id="bulkEliminateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Eliminate Selected Applicants</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkEliminateForm">
                    <div class="form-group">
                        <label for="bulkEliminateReason">Reason for Elimination</label>
                        <textarea class="form-control" id="bulkEliminateReason" name="eliminateReason" required></textarea>
                    </div>
                    <input type="hidden" id="bulkEliminateIds" name="applicantIds">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmBulkEliminate">Eliminate</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Withdraw Modal -->
<div class="modal fade" id="bulkWithdrawModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Withdraw Selected Applicants</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkWithdrawForm">
                    <div class="form-group">
                        <label for="bulkWithdrawReason">Reason for Withdrawal</label>
                        <textarea class="form-control" id="bulkWithdrawReason" name="withdrawReason" required></textarea>
                    </div>
                    <input type="hidden" id="bulkWithdrawIds" name="applicantIds">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmBulkWithdraw">Withdraw</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Handle "Select All" checkbox
    $('#headerCheckbox').change(function() {
        $('.applicant-checkbox').prop('checked', $(this).is(':checked'));
    });

    // Handle bulk action button click
    $('#applyBulkAction').click(function() {
        const selectedAction = $('#bulkActionSelect').val();
        const selectedApplicants = $('.applicant-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (!selectedAction) {
            alert('Please select an action');
            return;
        }

        if (selectedApplicants.length === 0) {
            alert('Please select at least one applicant');
            return;
        }

        // Show appropriate modal based on selected action
        switch(selectedAction) {
            case 'shortlist':
                $('#bulkShortlistIds').val(selectedApplicants.join(','));
                $('#bulkShortlistModal').modal('show');
                break;
            case 'eliminate':
                $('#bulkEliminateIds').val(selectedApplicants.join(','));
                $('#bulkEliminateModal').modal('show');
                break;
            case 'withdraw':
                $('#bulkWithdrawIds').val(selectedApplicants.join(','));
                $('#bulkWithdrawModal').modal('show');
                break;
        }
    });

    // Handle bulk shortlist confirmation
    $('#confirmBulkShortlist').click(function() {
        const ids = $('#bulkShortlistIds').val();
        const reason = $('#bulkShortlistReason').val();
        
        if (!reason) {
            alert('Please provide a reason');
            return;
        }

        $.ajax({
            url: '<?= base_url('reports/bulk_shortlist') ?>',
            method: 'POST',
            data: {
                applicantIds: ids,
                shortlistReason: reason,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>' // Add CSRF token
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.error || 'Failed to shortlist applicants');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                alert('An error occurred: ' + (xhr.responseJSON?.error || error));
            }
        });
    });

    // Handle bulk eliminate confirmation
    $('#confirmBulkEliminate').click(function() {
        const ids = $('#bulkEliminateIds').val();
        const reason = $('#bulkEliminateReason').val();
        
        if (!reason) {
            alert('Please provide a reason');
            return;
        }

        $.ajax({
            url: '<?= base_url('reports/bulk_eliminate') ?>',
            method: 'POST',
            data: {
                applicantIds: ids,
                eliminateReason: reason,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>' // Add CSRF token
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.error || 'Failed to eliminate applicants');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                alert('An error occurred: ' + (xhr.responseJSON?.error || error));
            }
        });
    });

    // Handle bulk withdraw confirmation
    $('#confirmBulkWithdraw').click(function() {
        const ids = $('#bulkWithdrawIds').val();
        const reason = $('#bulkWithdrawReason').val();
        
        if (!reason) {
            alert('Please provide a reason');
            return;
        }

        $.ajax({
            url: '<?= base_url('reports/bulk_withdraw') ?>',
            method: 'POST',
            data: {
                applicantIds: ids,
                withdrawReason: reason,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>' // Add CSRF token
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.error || 'Failed to withdraw applicants');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                alert('An error occurred: ' + (xhr.responseJSON?.error || error));
            }
        });
    });
});
</script>
<?= $this->endSection() ?>