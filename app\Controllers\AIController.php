<?php

namespace App\Controllers;

use App\Models\DakoiiOrgModel;

class AIController extends BaseController
{
    protected $dakoiiOrgModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->dakoiiOrgModel = new DakoiiOrgModel();
    }

    public function aiAnalysis()
    {
        // Start timing
        $startTime = microtime(true);

        // Check if this is an AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request method'
            ]);
        }

        // Get the prompt from the POST data
        $prompt = $this->request->getPost('prompt');

        if (empty($prompt)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'No prompt provided'
            ]);
        }

        try {
            // Get the organization's AI model preference
            $org = $this->dakoiiOrgModel->where('id', session('org_id'))->first();
            $aiModel = $org['ai_model'] ?? 'anthropic'; // Default to anthropic if not set

            // Use the specified model
            $result = null;
            switch ($aiModel) {
                case 'gemini':
                    $result = $this->useGeminiAI($prompt);
                    break;
                case 'deepseek':
                    $result = $this->useDeepSeekAI($prompt);
                    break;
                case 'anthropic':
                default:
                    $result = $this->useAnthropicAI($prompt);
                    break;
            }

            // Calculate processing time
            $endTime = microtime(true);
            $processingTime = round($endTime - $startTime, 2);

            // Add processing time to the response
            $decodedResult = json_decode($result->getJSON(), true);
            $decodedResult['processingTime'] = $processingTime;
            $decodedResult['model'] = $aiModel;

            return $this->response->setJSON($decodedResult);

        } catch (\Exception $e) {
            log_message('error', 'AI Analysis Error: ' . $e->getMessage());
            
            // Calculate processing time even for errors
            $endTime = microtime(true);
            $processingTime = round($endTime - $startTime, 2);

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'AI Error: ' . $e->getMessage(),
                'processingTime' => $processingTime,
                'model' => $aiModel ?? 'unknown'
            ]);
        }
    }

    private function useAnthropicAI($prompt)
    {
        // Anthropic AI API configuration
        $apiKey = '************************************************************************************************************';
        $apiUrl = 'https://api.anthropic.com/v1/messages';

        // Prepare the request data
        $data = [
            "model" => "claude-3-5-sonnet-20241022",
            "max_tokens" => 2000,
            'temperature' => 0.0,
            "system" => "You are a helpful assistant that analyzes job applicants against position requirements. You must provide responses in plain text only. Do not use any markdown, HTML, or other formatting. Use simple line breaks and spaces for formatting.",
            "messages" => [
                [
                    "role" => "user",
                    "content" => $prompt
                ]
            ]
        ];

        // Initialize cURL session
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'x-api-key: ' . $apiKey,
            'anthropic-version: 2023-06-01'
        ]);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Execute the request
        $response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \Exception('Connection Error: ' . $error);
        }

        curl_close($ch);

        // Decode the response
        $decodedResponse = json_decode($response, true);

        // Check for API errors
        if (isset($decodedResponse['error'])) {
            throw new \Exception('API Error: ' . ($decodedResponse['error']['message'] ?? 'Unknown error'));
        }

        // Extract and clean the AI's response
        $aiResponse = $decodedResponse['content'][0]['text'] ?? null;
        if (!$aiResponse) {
            throw new \Exception('No response from Anthropic AI');
        }

        // Clean any potential markdown formatting
        $aiResponse = preg_replace('/[*_`#]/', '', $aiResponse);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => $aiResponse
        ]);
    }

    private function useGeminiAI($prompt)
    {
        // Gemini AI API configuration
        $apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        $apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/learnlm-1.5-pro-experimental:generateContent';

        // Prepare the request data with enhanced configuration
        $data = [
            "contents" => [
                [
                    "parts" => [
                        [
                            "text" => "You are a helpful assistant that analyzes job applicants against position requirements. You must provide responses in plain text only. Do not use any markdown, HTML, or other formatting. Use simple line breaks and spaces for formatting. Here is the request: " . $prompt
                        ]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 2048,
                "stopSequences" => []
            ],
            "safetySettings" => [
                [
                    "category" => "HARM_CATEGORY_HARASSMENT",
                    "threshold" => "BLOCK_NONE"
                ],
                [
                    "category" => "HARM_CATEGORY_HATE_SPEECH",
                    "threshold" => "BLOCK_NONE"
                ],
                [
                    "category" => "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold" => "BLOCK_NONE"
                ],
                [
                    "category" => "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold" => "BLOCK_NONE"
                ]
            ]
        ];

        // Initialize cURL session
        $ch = curl_init($apiUrl . '?key=' . $apiKey);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Execute the request
        $response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \Exception('Connection Error: ' . $error);
        }

        curl_close($ch);

        // Decode the response
        $decodedResponse = json_decode($response, true);

        // Check for API errors
        if (isset($decodedResponse['error'])) {
            throw new \Exception('API Error: ' . ($decodedResponse['error']['message'] ?? 'Unknown error'));
        }

        // Extract and clean the AI's response
        $aiResponse = $decodedResponse['candidates'][0]['content']['parts'][0]['text'] ?? null;
        if (!$aiResponse) {
            throw new \Exception('No response from Gemini AI');
        }

        // Clean any potential markdown formatting
        $aiResponse = preg_replace('/[*_`#]/', '', $aiResponse);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => $aiResponse
        ]);
    }

    private function useDeepSeekAI($prompt)
    {
        // DeepSeek AI API configuration using OpenAI compatible endpoint
        $apiKey = 'sk-c4d263a27fd44b308d58670147bacefb';
        $apiUrl = 'https://api.deepseek.com/v1/chat/completions';

        // Prepare the request data in OpenAI compatible format
        $data = [
            "model" => "deepseek-chat",
            "messages" => [
                [
                    "role" => "system",
                    "content" => "You are a helpful assistant that analyzes job applicants against position requirements. You must provide responses in plain text only. Do not use any markdown, HTML, or other formatting. Use simple line breaks and spaces for formatting."
                ],
                [
                    "role" => "user",
                    "content" => $prompt
                ]
            ],
            "temperature" => 0,
            "max_tokens" => 2000,
            "stream" => false
        ];

        // Initialize cURL session with OpenAI compatible headers
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ]);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Execute the request
        $response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \Exception('Connection Error: ' . $error);
        }

        curl_close($ch);

        // Decode the response
        $decodedResponse = json_decode($response, true);

        // Check for API errors
        if (isset($decodedResponse['error'])) {
            throw new \Exception('API Error: ' . ($decodedResponse['error']['message'] ?? 'Unknown error'));
        }

        // Extract and clean the AI's response
        $aiResponse = $decodedResponse['choices'][0]['message']['content'] ?? null;
        if (!$aiResponse) {
            throw new \Exception('No response from DeepSeek AI');
        }

        // Clean any potential markdown formatting
        $aiResponse = preg_replace('/[*_`#]/', '', $aiResponse);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => $aiResponse
        ]);
    }
} 