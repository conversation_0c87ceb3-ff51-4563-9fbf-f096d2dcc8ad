<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= esc($title) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item active">Pre-Selection Report</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content" id="printableContent">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card p-3 mb-3">
                        <div class="row">
                            <div class="col-12 text-center">
                                <img src="<?= imgcheck($org['org_logo']) ?>" alt="PNG Emblem" width="100">
                                <h4 class="mt-3"><?= strtoupper($org['name']) ?></h4>
                                <hr style="border: none; height: 2px; background-color: #333; margin: 20px auto; width: 80%;">
                                <h5>PRE-SELECTION AND SHORT-LISTING REPORT</h5>
                                <p class="float-right">FORM RS 3.7(A)</p>
                            </div>
                        </div>

                        <!-- Introduction Section -->
                        <div class="mt-4">
                            <h5>1. Introduction</h5>
                            <div class="summernote"><?= $org['introduction'] ?></div>
                        </div>

                        <!-- Composition of Pre-Selection Committee -->
                        <div class="mt-4">
                            <h5>2. Composition of Pre-Selection Committee</h5>
                            <div class="summernote"><?= $org['composition'] ?></div>
                        </div>

                        <!-- Selection Criteria -->
                        <div class="mt-4">
                            <h5>3. Selection Criteria</h5>
                            <div class="summernote"><?= $org['criteria'] ?></div>
                        </div>

                        <!-- Culling and Pre-Selection Shortlisting -->
                        <div class="mt-4">
                            <h5>4. Culling and Pre-Selection Shortlisting</h5>
                            <div class="summernote"><?= $org['culling'] ?></div>
                        </div>

                        <!-- Summary Table -->
                        <div class="mt-4">
                            <h5>Summary</h5>
                            <table class="table table-bordered" id="summaryTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Position No/Grade</th>
                                        <th>Designation</th>
                                        <th>Total Applications</th>
                                        <th>Shortlisted</th>
                                        <th>Elimination</th>
                                        <th>Withdrawn</th>
                                        <th>Remarks</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $loop = 1;
                                     foreach ($positionGroups as $group): ?>
                                        <?php 
                                            
                                            foreach ($group['positions'] as $position): ?>
                                            <tr <?= isset($position['for_interview']) && $position['for_interview'] == 1 ? 'style="background-color: #e6ffe6;"' : '' ?>>
                                                <td><?= $loop++ ?></td>
                                                <td><?= esc($position['position_no'] ?? 'N/A') ?> / <?= esc($position['classification'] ?? 'N/A') ?></td>
                                                <td><?= esc($position['designation'] ?? 'N/A') ?></td>
                                                <td><?= $position['totalApplicants'] ?></td>
                                                <td><?= $position['shortlistedCount'] ?></td>
                                                <td><?= $position['eliminatedCount'] ?></td>
                                                <td><?= $position['withdrawnCount'] ?></td>
                                                <td>
                                                    <span data-toggle="modal" data-target="#remarksModal<?= $position['id'] ?>" style="cursor: pointer;">
                                                        <?= empty($position['remarks']) ? 'Add Remarks' : esc($position['remarks']) ?>
                                                    </span>
                                                    
                                                    <!-- Modal -->
                                                    <div class="modal fade" id="remarksModal<?= $position['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="remarksModalLabel<?= $position['id'] ?>" aria-hidden="true">
                                                        <div class="modal-dialog" role="document">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="remarksModalLabel<?= $position['id'] ?>">Remarks for <?= esc($position['position_no'] ?? 'N/A') ?></h5>
                                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <?= form_open('reports/saveRemarks') ?>
                                                                <div class="modal-body">
                                                                    <textarea class="form-control" name="remarks" id="remarksText<?= $position['id'] ?>" rows="3"><?= esc($position['remarks'] ?? '') ?></textarea>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <input type="hidden" name="positionId" value="<?= $position['id'] ?>">
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                                    <button type="submit" class="btn btn-primary">Save changes</button>
                                                                </div>
                                                                <?= form_close() ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="3">Total</th>
                                        <th><?= $totalApplicants ?></th>
                                        <th><?= $shortlistedCount ?></th>
                                        <th><?= $eliminatedCount ?></th>
                                        <th><?= $withdrawnCount ?></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <!-- signatures -->
                        <div class="row">
                            <div class="col-12 text-center">
                                <h5 class="mt-5">Signatures:</h5>
                                <br>
                                <div class="row mt-4">
                                    <?php
                                    $composition = preg_split('/<br>|<\/p>/', $org['composition']);
                                    $composition = array_filter(array_map('trim', $composition));
                                    $composition = array_map(function ($item) {
                                        return strpos($item, '<p>') === false ? "<p>$item</p>" : $item;
                                    }, $composition);
                                    foreach ($composition as $member) :
                                        $member = trim(strip_tags($member));
                                        if (!empty($member)) :
                                    ?>
                                            <div class="col-4 mb-4">
                                                <br>
                                                <div class="border-top pt-2" style="width: 200px; margin: 0 auto;">
                                                    <p class="mb-0"><?= esc($member) ?></p>
                                                </div>
                                            </div>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </div>
                                <div class="row">

                                    <div class="col-12 text-left">
                                        <br>
                                        <strong>Date:...............................................</strong>
                                    </div>
                                </div>
                            </div>

                            <!-- Print Button -->
                            <div class="row no-print mt-4">
                                <div class="col-12">
                                    <a href="javascript:window.print();" class="btn btn-default">
                                        <i class="fas fa-print"></i> Print
                                    </a>
                                    <button onclick="copyTableToClipboard()" class="btn btn-default">
                                        <i class="fas fa-copy"></i> Copy Table
                                    </button>
                                    <button onclick="copyWholePage()" class="btn btn-default">
                                        <i class="fas fa-copy"></i> Copy Page
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </section>
</div>

<!-- Add this script before the endSection -->
<script>
    function copyTableToClipboard() {
        // Get the table element
        const table = document.getElementById('summaryTable');
        
        // Create a range and select the table
        const range = document.createRange();
        range.selectNode(table);
        
        // Get the selection object and add the range to it
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
        
        // Execute the copy command
        try {
            document.execCommand('copy');
            toastr.success('Table copied to clipboard! You can paste it into Word or Excel.');
        } catch (err) {
            toastr.error('Failed to copy the table.');
        }
        
        // Clear the selection
        selection.removeAllRanges();
    }

    function copyWholePage() {
        // Get the content wrapper element (which contains the printable content)
        const contentWrapper = document.getElementById('printableContent');
        
        // Create a new temporary element
        const tempElement = document.createElement('div');
        tempElement.innerHTML = contentWrapper.innerHTML;
        
        // Remove any no-print elements from the temporary element
        const noPrintElements = tempElement.querySelectorAll('.no-print');
        noPrintElements.forEach(element => element.remove());
        
        // Append the temporary element to the document
        document.body.appendChild(tempElement);
        
        // Select the content of the temporary element
        const range = document.createRange();
        range.selectNode(tempElement);
        
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
        
        // Copy the content to clipboard
        try {
            document.execCommand('copy');
            toastr.success('Page content copied! You can paste it into Word.');
        } catch (err) {
            toastr.error('Failed to copy the page content.');
        }
        
        // Remove the temporary element
        document.body.removeChild(tempElement);
        
        // Clear the selection
        selection.removeAllRanges();
    }
</script>

<?= $this->endSection() ?>
