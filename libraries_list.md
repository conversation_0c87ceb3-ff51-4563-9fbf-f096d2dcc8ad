# Libraries in SelMasta Five Application

This document lists the libraries that are present in the application.

## Present Libraries

### Core Libraries
1. **CodeIgniter 4 Framework** (v4.6.1)
   - The main PHP framework used in the application

### PHP Libraries (via Composer)
1. **laminas/laminas-escaper** (^2.14)
   - Used for HTML, URL, JS, CSS, and attribute escaping
   - Required by CodeIgniter 4

2. **psr/log** (^3.0)
   - PSR-3 logger interface
   - Required by CodeIgniter 4

3. **tecnickcom/tcpdf** (^6.9)
   - PDF generation library
   - Used in InterviewController for generating notification PDFs
   - Recently installed

4. **FPDF**
   - Another PDF library
   - Located in app/ThirdParty/fpdf/fpdf.php

5. **bacon/bacon-qr-code** (^3.0)
   - QR code generator for PHP
   - Recently installed

6. **chillerlan/php-qrcode** (^5.0)
   - Another QR code generator with user-friendly API
   - Recently installed

7. **endroid/qr-code** (^6.0)
   - Endroid QR Code library
   - Recently installed

8. **chillerlan/php-settings-container** (3.2.1)
   - Settings container for chillerlan/php-qrcode
   - Installed as a dependency

9. **dasprid/enum** (1.0.6)
   - Enumeration implementation for PHP
   - Installed as a dependency

10. **myclabs/deep-copy** (^1.13)
    - Deep copy (clone) utility
    - Recently installed

11. **psr/container** (^2.0)
    - PSR-11 container interface
    - Recently installed

12. **symfony/deprecation-contracts** (^3.5)
    - Symfony deprecation contracts
    - Recently installed

13. **predis/predis** (^3.0)
    - Redis client
    - Recently installed

### Development Libraries (require-dev)
1. **fakerphp/faker** (^1.9)
   - Fake data generator for testing

2. **mikey179/vfsstream** (^1.6)
   - Virtual file system for testing

3. **phpunit/phpunit** (^10.5.16)
   - Testing framework

4. **codeigniter/coding-standard** (^1.8)
   - Coding standards for CodeIgniter
   - Recently installed

5. **friendsofphp/php-cs-fixer** (^3.75)
   - PHP coding standards fixer
   - Recently installed

6. **nexusphp/cs-config** (^3.25)
   - Coding standards configuration
   - Recently installed

### Frontend Libraries (AdminLTE 3.2.0 Theme)
1. **Bootstrap** (^4.6.1)
   - Frontend framework

2. **jQuery** (^3.6.0)
   - JavaScript library

3. **Popper.js** (^1.16.1)
   - Required for Bootstrap tooltips and popovers

4. **@fortawesome/fontawesome-free** (^5.15.4)
   - Icon library

5. **datatables.net** (^1.11.4) and various extensions
   - Interactive tables

6. **chart.js** (^2.9.4)
   - Charting library

7. **sweetalert2** (^11.4.0)
   - Enhanced alert dialogs

8. **toastr** (^2.1.4)
   - Notification library

9. **summernote** (^0.8.20)
   - WYSIWYG editor

10. **moment** (^2.29.1)
    - Date manipulation library

11. **pdfmake** (^0.2.4)
    - Client-side PDF generation

12. **marked** and **DOMPurify**
    - Used for Markdown parsing and sanitization in the AI chat feature

## Notes

- The application now has multiple QR code libraries available:
  - bacon/bacon-qr-code
  - chillerlan/php-qrcode
  - endroid/qr-code
  - TCPDF's built-in QR code functionality
- Email functionality uses CodeIgniter's built-in Email service with SMTP configuration in app/Config/Email.php
- A custom helper function `sendmail()` is defined in app/Helpers/info_helper.php for sending emails
- PDF generation in the InterviewController now works correctly with the installed TCPDF library
