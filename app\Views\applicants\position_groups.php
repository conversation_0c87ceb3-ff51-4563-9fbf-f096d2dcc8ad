<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Position Groups</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-layer-group mr-2"></i>
                                Position Groups
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped text-nowrap">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Group Name</th>
                                            <th>Description</th>
                                            <th>Applicants</th>
                                            <th>Post.No.Applicants</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($groups as $group): ?>
                                        <tr>
                                            <td><?= esc($group['group_name']) ?></td>
                                            <td><?= esc($group['description']) ?></td>
                                            <td>
                                                <?php
                                                $applicantCount = 0;
                                                foreach ($applicants as $applicant) {
                                                    if ($applicant['position_group_id'] == $group['id']) {
                                                        $applicantCount++;
                                                    }
                                                }
                                                echo esc($applicantCount);
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $positionCount = 0;
                                                foreach ($positions as $position) {
                                                    if ($position['position_group_id'] == $group['id']) {
                                                        $positionCount++;
                                                    }
                                                }
                                                echo esc($positionCount);
                                                ?>
                                            </td>
                                            <td>
                                                <a href="<?= site_url('applicants/viewPositions/' . $group['id']) ?>" class="btn btn-primary btn-sm">View Positions</a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <!-- what are you looking at? -->
                                    
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
<?= $this->endSection() ?>
