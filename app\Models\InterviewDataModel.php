<?php

namespace App\Models;

use CodeIgniter\Model;

class InterviewDataModel extends Model
{
    protected $table      = 'interview_data';      // Name of the table
    protected $primaryKey = 'id';                  // Primary key of the table

    protected $useAutoIncrement = true;            // Primary key is auto-incremented

    // Fields that are allowed to be inserted/updated
    protected $allowedFields = [
        'position_id',
        'interviewer_id',
        'applicant_id',
        'question_id',
        'score',
        'comments',
        'created_by',
        'updated_by',
        'is_deleted',
        'deleted_at',
        'deleted_by'
    ];

    // Automatically handle the timestamps for created_at and updated_at
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Set up soft deletes to retain deleted records
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';

    // Define default values for specific fields
    protected $defaultValues = [
        'score' => 0.00,
        'is_deleted' => false
    ];

    // You can add custom methods here if needed
    
    /**
     * Get interview data by position ID
     *
     * @param int $positionId
     * @return array
     */
    public function getDataByPosition($positionId)
    {
        return $this->where('position_id', $positionId)
                    ->where('is_deleted', false)
                    ->findAll();
    }
    
    /**
     * Get interview data by applicant ID
     *
     * @param int $applicantId
     * @return array
     */
    public function getDataByApplicant($applicantId)
    {
        return $this->where('applicant_id', $applicantId)
                    ->where('is_deleted', false)
                    ->findAll();
    }
    
    /**
     * Get interview data by interviewer ID
     *
     * @param int $interviewerId
     * @return array
     */
    public function getDataByInterviewer($interviewerId)
    {
        return $this->where('interviewer_id', $interviewerId)
                    ->where('is_deleted', false)
                    ->findAll();
    }
    
    /**
     * Get interview data by question ID
     *
     * @param int $questionId
     * @return array
     */
    public function getDataByQuestion($questionId)
    {
        return $this->where('question_id', $questionId)
                    ->where('is_deleted', false)
                    ->findAll();
    }
    
    /**
     * Get average score for an applicant across all questions
     *
     * @param int $applicantId
     * @return float
     */
    public function getApplicantAverageScore($applicantId)
    {
        $result = $this->selectAvg('score')
                       ->where('applicant_id', $applicantId)
                       ->where('is_deleted', false)
                       ->first();

        return $result ? $result['score'] : 0;
    }

    /**
     * Get interview data for specific combination
     *
     * @param int $applicantId
     * @param int $positionId
     * @param int $interviewerId
     * @param int $questionId
     * @return array|null
     */
    public function getSpecificData($applicantId, $positionId, $interviewerId, $questionId)
    {
        return $this->where([
            'applicant_id' => $applicantId,
            'position_id' => $positionId,
            'interviewer_id' => $interviewerId,
            'question_id' => $questionId,
            'is_deleted' => false
        ])->first();
    }
}