<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('applicants') ?>">Positions</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('applicants/list/' . $position['id']) ?>"><?= esc($position['designation']) ?></a></li>
                        <li class="breadcrumb-item active">Import</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-file-import mr-2"></i>
                                Import Applicants for (<?= esc($position['position_no']) ?>) <?= esc($position['designation']) ?>
                            </h3>
                        </div>
                        <div class="card-body">
                            <?= form_open_multipart('applicants/import/' . $position['id']) ?>
                                <div class="form-group">
                                    <label for="csv_file">CSV File</label>
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input" id="csv_file" name="csv_file" required accept=".csv">
                                        <label class="custom-file-label" for="csv_file">Choose file</label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">Import</button>
                            <?= form_close() ?>
                            <hr>
                            <a href="<?= base_url('public/uploads/applicants_template.csv') ?>" class="btn btn-info">Download CSV Template</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
