<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-cog mr-2"></i>
                        System Settings
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="mb-3">
                <a href="<?= base_url('settings/interviews') ?>" class="btn btn-info btn-lg mr-2">
                    <i class="fas fa-calendar-alt mr-2"></i> Interview Settings
                </a>
                <a href="<?= base_url('settings/pre_selection_report') ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-file-alt mr-2"></i> Pre-Selection Report
                </a>
            </div>
            
            <!-- Advertisement Settings -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bullhorn mr-2"></i>
                                Advertisement Settings
                            </h3>
                        </div>
                        <?= form_open('settings_advertisement') ?>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="advertisement_no">Advertisement Number</label>
                                <input type="text" class="form-control" id="advertisement_no" name="advertisement_no" 
                                       value="<?= esc($org['advertisement_no']) ?>" placeholder="Enter advertisement number">
                            </div>
                            <div class="form-group">
                                <label for="advertisement_date">Advertisement Date</label>
                                <input type="date" class="form-control" id="advertisement_date" name="advertisement_date" 
                                       value="<?= esc($org['advertisement_date']) ?>">
                            </div>
                            <div class="form-group">
                                <label for="mode_of_advert">Mode of Advertisement</label>
                                <input type="text" class="form-control" id="mode_of_advert" name="mode_of_advert" 
                                       value="<?= esc($org['mode_of_advert']) ?>" placeholder="Enter mode of advertisement">
                            </div>
                            <div class="form-group">
                                <label for="sort_out_of">Sort Out Of</label>
                                <input type="number" class="form-control" id="sort_out_of" name="sort_out_of" 
                                       value="<?= esc($org['sort_out_of']) ?>" placeholder="Enter sort out number">
                            </div>
                        </div>
                        <div class="card-footer bg-white">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i> Save Changes
                            </button>
                        </div>
                        </form>
                    </div>

                    <!-- Organization Logo -->
                    <div class="card card-primary card-outline">
                        <div class="card-header d-flex align-items-center">
                            <h3 class="card-title">
                                <i class="fas fa-image mr-2"></i>
                                Organization Logo
                            </h3>
                            <?php if ($org['org_logo']): ?>
                                <img src="<?= imgcheck($org['org_logo']) ?>" alt="Logo" class="ml-auto" style="width: 40px; height: 40px; object-fit: contain;">
                            <?php endif; ?>
                        </div>
                        <?= form_open_multipart('settings_logo') ?>
                        <div class="card-body">
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="logo" name="logo" accept="image/*">
                                <label class="custom-file-label" for="logo">Choose file</label>
                            </div>
                            <small class="form-text text-muted mt-2">
                                Recommended size: 200x200 pixels. Maximum file size: 2MB.
                            </small>
                        </div>
                        <div class="card-footer bg-white">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload mr-1"></i> Upload Logo
                            </button>
                        </div>
                        </form>
                    </div>
                </div>

                
            </div>
        </div>
    </section>
</div>

<style>
.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control {
    border-radius: 0.25rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-file-label {
    overflow: hidden;
}

textarea {
    min-height: 120px;
}

.note-editor.note-frame {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.note-editor.note-frame .note-editing-area {
    background-color: #fff;
}
</style>

<script>
$(function() {
    // Initialize all Summernote editors with consistent options
    $('textarea').each(function() {
        $(this).summernote({
            height: 120,
            toolbar: [
                ['style', ['bold', 'italic', 'underline', 'clear']],
                ['para', ['ul', 'ol']],
            ],
            callbacks: {
                onChange: function(contents) {
                    $(this).val(contents);
                }
            }
        });
    });

    // Update custom file input label
    bsCustomFileInput.init();
});
</script>

<?= $this->endSection() ?>