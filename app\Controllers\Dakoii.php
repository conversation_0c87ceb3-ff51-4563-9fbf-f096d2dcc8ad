<?php

namespace App\Controllers;

use App\Models\dakoiiUsersModel;
use App\Models\DakoiiOrgModel;
use App\Models\UsersModel;

class <PERSON><PERSON><PERSON> extends BaseController
{
    protected $session;
    protected $dakoii_usersModel;
    protected $usersModel;
    protected $dakoiiOrgModel;
    protected $orgUsersModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dakoii_usersModel = new dakoiiUsersModel();
        $this->usersModel = new usersModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
        $this->orgUsersModel = new UsersModel();
    }

    // Authentication
    public function index()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dakoii_login";
        return view('dakoii/login', $data);
    }

    public function login()
    {
        $username = $this->request->getVar('username');
        $password = $this->request->getVar('password');

        $user = $this->dakoii_usersModel->where('username', $username)->first();

        if (!$user || !password_verify($password, $user['password'])) {
            return redirect()->back()->with('error', 'Invalid Username or Password');
        }

        $this->session->set([
            'user_id' => $user['id'],
            'user_name' => $user['name'],
            'user_role' => $user['role'],
            'user_type' => "dakoii",
            'user_username' => $user['username'],
            'user_email' => $user['username'],
            'is_loggedin' => "yes"
        ]);

        return redirect()->to('dakoii/dashboard');
    }

    public function logout()
    {
        $this->session->destroy();
        return redirect()->to(base_url());
    }

    // Dashboard
    public function dashboard()
    {
        $data = [
            'title' => "Dakoii Dashboard",
            'menu' => "dakoii_dashboard",
            'dusers' => $this->dakoii_usersModel->findAll(),
            'organizations' => $this->dakoiiOrgModel->findAll(),
            'user' => [
                'name' => $this->session->get('user_name'),
                'username' => $this->session->get('user_username'),
                'role' => $this->session->get('user_role')
            ]
        ];

        $this->session->set('org_id', "");
        return view('dakoii/dashboard', $data);
    }

    // Admin User Management
    public function adduser()
    {
        // Validate the request
        $rules = [
            'username' => 'required|is_unique[dakoii_users.username]',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->to('ddash')->with('error', 'Username already exists or validation failed');
        }

        $data = [
            'name' => $this->request->getVar('name'),
            'username' => $this->request->getVar('username'),
            'password' => password_hash($this->request->getVar('password'), PASSWORD_DEFAULT),
            'role' => $this->request->getVar('role'),
            'is_active' => $this->request->getVar('is_active') ?? "0",
        ];

        $this->dakoii_usersModel->insert($data);
        return redirect()->to('ddash')->with('success', 'Admin Created');
    }

    // Organization Management
    public function createOrg()
    {
        $data = [
            'orgcode' => $this->request->getPost('orgcode'),
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'license_status' => $this->request->getPost('license_status'),
            'ai_model' => $this->request->getPost('ai_model') ?? 'anthropic' // Default to anthropic if not set
        ];

        // Handle org_logo upload if present
        $org_logo = $this->request->getFile('org_logo');
        if ($org_logo && $org_logo->isValid() && !$org_logo->hasMoved()) {
            // Create upload directory if it doesn't exist
            $uploadPath = FCPATH . 'public/uploads/org_logo';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Generate a unique name with timestamp
            $newName = $org_logo->getRandomName();

            // Move the file to the upload directory
            if ($org_logo->move($uploadPath, $newName)) {
                // Store the full path in the database
                $data['org_logo'] = '/public/uploads/org_logo/' . $newName;
            } else {
                // Log the error if file upload fails
                log_message('error', 'Failed to upload organization logo: ' . $org_logo->getErrorString());
                return redirect()->back()->withInput()->with('error', 'Failed to upload logo: ' . $org_logo->getErrorString());
            }
        }

        if ($this->dakoiiOrgModel->insert($data)) {
            return redirect()->to('dakoii/dashboard')->with('success', 'Organization created successfully');
        } else {
            return redirect()->back()->withInput()->with('errors', $this->dakoiiOrgModel->errors());
        }
    }

    public function viewOrg($id)
    {
        $org = $this->dakoiiOrgModel->find($id);
        if ($org) {
            $data = [
                'title' => "View Organization",
                'menu' => "view_org",
                'org' => $org,
                'users' => $this->orgUsersModel->where('org_id', $id)->findAll()
            ];
            return view('dakoii/view_org', $data);
        } else {
            return redirect()->to('dakoii/dashboard')->with('error', 'Organization not found');
        }
    }

    public function updateOrg($id)
    {
        $data = [
            'orgcode' => $this->request->getPost('orgcode'),
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'license_status' => $this->request->getPost('license_status'),
            'ai_model' => $this->request->getPost('ai_model') ?? 'anthropic' // Default to anthropic if not set
        ];

        // Handle org_logo upload
        $org_logo = $this->request->getFile('org_logo');
        if ($org_logo && $org_logo->isValid() && !$org_logo->hasMoved()) {
            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/org_logo';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Generate a unique name with timestamp
            $newName = $org_logo->getRandomName();

            // Move the file to the upload directory
            if ($org_logo->move($uploadPath, $newName)) {
                // Store the full path in the database
                $data['org_logo'] = 'public/uploads/org_logo/' . $newName;
            } else {
                // Log the error if file upload fails
                log_message('error', 'Failed to upload organization logo: ' . $org_logo->getErrorString());
                return redirect()->back()->withInput()->with('error', 'Failed to upload logo: ' . $org_logo->getErrorString());
            }
        }

        // Verify the organization exists before updating
        if (!$this->dakoiiOrgModel->find($id)) {
            return redirect()->to('dakoii/dashboard')->with('error', 'Organization not found');
        }

        if ($this->dakoiiOrgModel->update($id, $data)) {
            return redirect()->to('dakoii/dashboard')->with('success', 'Organization updated successfully');
        } else {
            return redirect()->back()->withInput()->with('errors', $this->dakoiiOrgModel->errors());
        }
    }

    public function deleteOrg($id)
    {
        if ($this->dakoiiOrgModel->delete($id)) {
            return redirect()->to('dakoii/dashboard')->with('success', 'Organization deleted successfully');
        } else {
            return redirect()->to('dakoii/dashboard')->with('error', 'Failed to delete organization');
        }
    }

    // Organization User Management
    public function createUser($org_id)
    {
        $data = [
            'org_id' => $org_id,
            'name' => $this->request->getPost('name'),
            'username' => $this->request->getPost('username'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
        ];

        if ($this->orgUsersModel->insert($data)) {
            return redirect()->to("dakoii/viewOrg/$org_id")->with('success', 'User created successfully');
        } else {
            return redirect()->back()->withInput()->with('errors', $this->orgUsersModel->errors());
        }
    }

    public function updateUser($id)
    {
        $data = [
            'name' => $this->request->getPost('name'),
            'username' => $this->request->getPost('username'),
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
        ];

        if ($this->request->getPost('password')) {
            $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        $user = $this->orgUsersModel->find($id);
        if ($user && $this->orgUsersModel->update($id, $data)) {
            return redirect()->to("dakoii/viewOrg/{$user['org_id']}")->with('success', 'User updated successfully');
        } else {
            return redirect()->back()->withInput()->with('errors', $this->orgUsersModel->errors());
        }
    }

    public function deleteUser($id)
    {
        $user = $this->orgUsersModel->find($id);
        if ($user && $this->orgUsersModel->delete($id)) {
            return redirect()->to("dakoii/viewOrg/{$user['org_id']}")->with('success', 'User deleted successfully');
        } else {
            return redirect()->back()->with('error', 'Failed to delete user');
        }
    }
}
