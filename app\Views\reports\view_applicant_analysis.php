<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-user-chart mr-2"></i>
                        Applicant Analysis Report
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("reports/viewPositions/{$position['position_group_id']}") ?>">View Positions</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("reports/analysisReport/{$position['position_group_id']}/{$position['id']}") ?>">Applicants Analysis</a></li>
                        <li class="breadcrumb-item active"><?= esc($applicant['name']) ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Summary Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Summary Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-box bg-gradient-primary">
                                <span class="info-box-icon"><i class="fas fa-star"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Score</span>
                                    <span class="info-box-number"><?= $totalScore ?> / <?= $maxTotalScore ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: <?= $maxTotalScore > 0 ? ($totalScore / $maxTotalScore) * 100 : 0 ?>%"></div>
                                    </div>
                                    <span class="progress-description">
                                        <?= $maxTotalScore > 0 ? number_format(($totalScore / $maxTotalScore) * 100, 1) : 0 ?>% Overall Performance
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box bg-gradient-success">
                                <span class="info-box-icon"><i class="fas fa-briefcase"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Position Applied</span>
                                    <span class="info-box-number"><?= esc($position['designation']) ?></span>
                                    <span class="progress-description">
                                        Position No: <?= esc($position['position_no']) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Analysis Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Detailed Rating Analysis
                    </h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th width="25%">Rating Criteria</th>
                                    <th width="15%">Score</th>
                                    <th width="15%">Maximum</th>
                                    <th width="15%">Percentage</th>
                                    <th>Analysis</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Age Rating -->
                                <tr>
                                    <td><strong>Age Rating</strong></td>
                                    <td class="text-center"><?= $applicant['rate_age'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_age'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_age'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_age'] ?? 0) / $applicant['max_rate_age']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>
                                        Age: <?= $applicant['age'] ?> years old
                                        <?php
                                        $ageScore = ($applicant['rate_age'] ?? 0);
                                        if ($ageScore >= 7) echo "<span class='badge badge-success'>Optimal Age Range</span>";
                                        elseif ($ageScore >= 4) echo "<span class='badge badge-info'>Acceptable Age Range</span>";
                                        else echo "<span class='badge badge-warning'>Sub-optimal Age Range</span>";
                                        ?>
                                    </td>
                                </tr>

                                <!-- Qualification Rating -->
                                <tr>
                                    <td><strong>Educational Qualification</strong></td>
                                    <td class="text-center"><?= $applicant['rate_qualification'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_qualification'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_qualification'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_qualification'] ?? 0) / $applicant['max_rate_qualification']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>
                                        <?= nl2br(esc($applicant['qualification_text'])) ?>
                                        <?php
                                        $qualScore = ($applicant['rate_qualification'] ?? 0);
                                        if ($qualScore >= 8) echo "<span class='badge badge-success'>Highly Qualified</span>";
                                        elseif ($qualScore >= 5) echo "<span class='badge badge-info'>Well Qualified</span>";
                                        else echo "<span class='badge badge-warning'>Minimum Qualification</span>";
                                        ?>
                                    </td>
                                </tr>

                                <!-- Experience Ratings -->
                                <tr class="table-secondary">
                                    <td colspan="5"><strong>Experience Breakdown</strong></td>
                                </tr>
                                <tr>
                                    <td>Private Sector (Relevant)</td>
                                    <td class="text-center"><?= $applicant['rate_private_relevant'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_private_relevant'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_private_relevant'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_private_relevant'] ?? 0) / $applicant['max_rate_private_relevant']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>Relevant private sector experience assessment</td>
                                </tr>
                                <tr>
                                    <td>Private Sector (Non-Relevant)</td>
                                    <td class="text-center"><?= $applicant['rate_private_non_relevant'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_private_non_relevant'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_private_non_relevant'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_private_non_relevant'] ?? 0) / $applicant['max_rate_private_non_relevant']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>Non-relevant private sector experience assessment</td>
                                </tr>
                                <tr>
                                    <td>Public Service (Relevant)</td>
                                    <td class="text-center"><?= $applicant['rate_public_relevant'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_public_relevant'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_public_relevant'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_public_relevant'] ?? 0) / $applicant['max_rate_public_relevant']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>Relevant public service experience assessment</td>
                                </tr>
                                <tr>
                                    <td>Public Service (Non-Relevant)</td>
                                    <td class="text-center"><?= $applicant['rate_public_non_relevant'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_public_non_relevant'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_public_non_relevant'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_public_non_relevant'] ?? 0) / $applicant['max_rate_public_non_relevant']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>Non-relevant public service experience assessment</td>
                                </tr>

                                <!-- Other Ratings -->
                                <tr>
                                    <td><strong>Training Relevance</strong></td>
                                    <td class="text-center"><?= $applicant['rate_trainings'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_trainings'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_trainings'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_trainings'] ?? 0) / $applicant['max_rate_trainings']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>
                                        <?= nl2br(esc($applicant['other_trainings'])) ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Skills & Competencies</strong></td>
                                    <td class="text-center"><?= $applicant['rate_skills_competencies'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_skills_competencies'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_skills_competencies'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_skills_competencies'] ?? 0) / $applicant['max_rate_skills_competencies']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>
                                        <?= nl2br(esc($applicant['skills_competencies'])) ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Knowledge Assessment</strong></td>
                                    <td class="text-center"><?= $applicant['rate_knowledge'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_knowledge'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_knowledge'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_knowledge'] ?? 0) / $applicant['max_rate_knowledge']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>
                                        <?= nl2br(esc($applicant['knowledge'])) ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Overall Capability</strong></td>
                                    <td class="text-center"><?= $applicant['rate_capability'] ?? 0 ?></td>
                                    <td class="text-center"><?= $applicant['max_rate_capability'] ?? 0 ?></td>
                                    <td class="text-center">
                                        <?= ($applicant['max_rate_capability'] ?? 0) > 0 ? 
                                            number_format((($applicant['rate_capability'] ?? 0) / $applicant['max_rate_capability']) * 100, 1) : 0 ?>%
                                    </td>
                                    <td>Overall capability assessment based on all factors</td>
                                </tr>
                            </tbody>
                            <tfoot class="table-dark">
                                <tr>
                                    <td><strong>TOTAL SCORE</strong></td>
                                    <td class="text-center"><strong><?= $totalScore ?></strong></td>
                                    <td class="text-center"><strong><?= $maxTotalScore ?></strong></td>
                                    <td class="text-center">
                                        <strong><?= $maxTotalScore > 0 ? number_format(($totalScore / $maxTotalScore) * 100, 1) : 0 ?>%</strong>
                                    </td>
                                    <td>
                                        <?php
                                        $percentage = $maxTotalScore > 0 ? ($totalScore / $maxTotalScore) * 100 : 0;
                                        if ($percentage >= 80) echo "<span class='badge badge-success'>Excellent Candidate</span>";
                                        elseif ($percentage >= 70) echo "<span class='badge badge-info'>Strong Candidate</span>";
                                        elseif ($percentage >= 60) echo "<span class='badge badge-primary'>Qualified Candidate</span>";
                                        else echo "<span class='badge badge-warning'>Below Expected Score</span>";
                                        ?>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Remarks Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-comments mr-2"></i>
                        Assessment Remarks
                    </h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($applicant['remarks'])): ?>
                        <div class="alert alert-info">
                            <?= nl2br(esc($applicant['remarks'])) ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            No specific remarks provided for this applicant.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.8rem;
}
.info-box-number {
    font-size: 1.5rem;
}
.progress {
    height: 4px;
}
.table td {
    vertical-align: middle;
}
</style>
<?= $this->endSection() ?> 