<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Rate Applicant: <?= esc($applicant['name']) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('ratings') ?>">Ratings</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("ratings/viewPositions/{$position['position_group_id']}") ?>">View Positions</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("ratings/viewApplicants/{$position['id']}") ?>">View Applicants</a></li>
                        <li class="breadcrumb-item active">Rate Applicant</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Position Specifications</h3>
                        </div>
                        <div class="card-body">
                            <h4><?= esc($position['designation']) ?></h4>
                            <p>Position No.: <?= esc($position['position_no']) ?></p>
                            <p>Classification: <?= esc($position['classification']) ?></p>
                            <p>Qualifications: <?= esc($position['qualifications']) ?></p>
                            <p>Knowledge: <?= esc($position['knowledge']) ?></p>
                            <p>Skills and Competencies: <?= esc($position['skills_competencies']) ?></p>
                            <p>Job Experiences: <?= esc($position['job_experiences']) ?></p>
                        </div>
                    </div>
                </div>
            
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Applicant Specifications</h3>
                        </div>
                        <div class="card-body">
                            <h4><?= esc($applicant['name']) ?></h4>
                            <p>Sex: <?= esc($applicant['sex']) ?></p>
                            <p>Age: <?= esc($applicant['age']) ?></p>
                            <p>Place of Origin: <?= esc($applicant['place_origin']) ?></p>
                            <p>Current Employer: <?= esc($applicant['current_employer']) ?></p>
                            <p>Current Position: <?= esc($applicant['current_position']) ?></p>
                            <p>Address/Location: <?= esc($applicant['address_location']) ?></p>
                            <p>Qualification: <?= esc($applicant['qualification_text']) ?></p>
                            <p>Other Trainings: <?= esc($applicant['other_trainings']) ?></p>
                            <p>Knowledge: <?= esc($applicant['knowledge']) ?></p>
                            <p>Skills and Competencies: <?= esc($applicant['skills_competencies']) ?></p>
                            <p>Job Experiences: <?= esc($applicant['job_experiences']) ?></p>
                            <p>Comments: <?= esc($applicant['comments']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Rate Applicant</h3>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url("ratings/rateApplicant/{$applicant['id']}") ?>" method="post">
                                <div class="form-group">
                                    <label for="rate_age">Age</label>
                                    <input type="number" class="form-control" id="rate_age" name="rate_age" min="0" max="100" value="<?= esc($applicant['rate_age']) ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="rate_qualification">Qualification</label>
                                    <input type="number" class="form-control" id="rate_qualification" name="rate_qualification" min="0" max="100" value="<?= esc($applicant['rate_qualification']) ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="rate_experience">Experience</label>
                                    <input type="number" class="form-control" id="rate_experience" name="rate_experience" min="0" max="100" value="<?= esc($applicant['rate_experience']) ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="rate_trainings">Trainings</label>
                                    <input type="number" class="form-control" id="rate_trainings" name="rate_trainings" min="0" max="100" value="<?= esc($applicant['rate_trainings']) ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="rate_skills_competencies">Skills and Competencies</label>
                                    <input type="number" class="form-control" id="rate_skills_competencies" name="rate_skills_competencies" min="0" max="100" value="<?= esc($applicant['rate_skills_competencies']) ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="rate_knowledge">Knowledge</label>
                                    <input type="number" class="form-control" id="rate_knowledge" name="rate_knowledge" min="0" max="100" value="<?= esc($applicant['rate_knowledge']) ?>" required>
                                </div>
                                <button type="submit" class="btn btn-primary">Submit Rating</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
