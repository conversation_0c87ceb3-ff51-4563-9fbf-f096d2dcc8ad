<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('applicants/positionGroups') ?>">Position Groups</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('applicants/viewPositions/' . $position['position_group_id']) ?>"><?= esc($position['group_name']) ?></a></li>
                        <li class="breadcrumb-item active"><?= esc($position['designation']) ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users mr-2"></i>
                                Applicants for (<?= esc($position['position_no']) ?>) <?= esc($position['designation']) ?>
                            </h3>
                            <div class="card-tools">
                                <div class="btn-group">
                                    <a href="<?= site_url('applicants/applicant_import/' . $position['id']) ?>" 
                                       class="btn btn-default btn-sm">
                                        <i class="fas fa-download mr-1"></i>
                                        Import Applicants
                                    </a>
                                    <a href="<?= site_url('applicants/create/' . $position['id']) ?>" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus mr-1"></i>
                                        Add Applicant
                                    </a>
                                    <a href="<?= site_url('applicants/import/' . $position['id']) ?>" 
                                       class="btn btn-success btn-sm">
                                        <i class="fas fa-file-csv mr-1"></i>
                                        Import CSV
                                    </a>
                                    <a href="<?= base_url('public/uploads/applicants_template.csv') ?>" 
                                       class="btn btn-info btn-sm">
                                        <i class="fas fa-download mr-1"></i>
                                        Template
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped text-nowrap">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Sex</th>
                                            <th>Age</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($applicants as $applicant): ?>
                                        <tr>
                                            <td><?= esc($applicant['name']) ?></td>
                                            <td><?= esc($applicant['sex']) ?></td>
                                            <td><?= esc($applicant['age']) ?></td>
                                            <td><?= $applicant['is_active'] ? 'Active' : 'Inactive' ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?= site_url('applicants/edit/' . $applicant['id']) ?>" 
                                                       class="btn btn-warning btn-sm" 
                                                       title="Edit Applicant">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="<?= site_url('applicants/delete/' . $applicant['id']) ?>" 
                                                       class="btn btn-danger btn-sm" 
                                                       onclick="return confirm('Are you sure you want to delete the applicant <?= esc($applicant['name']) ?>?')"
                                                       title="Delete Applicant">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
