<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Applicants for <?= esc($position['designation']) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('ratings') ?>">Ratings</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("ratings/viewPositions/{$position['position_group_id']}") ?>">View Positions</a></li>
                        <li class="breadcrumb-item active">View Applicants</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users mr-2"></i>
                                Applicants for (<?= esc($position['position_no']) ?>) <?= esc($position['designation']) ?>
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr class="text-nowrap">
                                            <th>Name</th>
                                            <th>Age</th>
                                            <th>Status</th>
                                            <th>Total Points</th>
                                            <th>Application Status</th>
                                            <th>Reasons</th>
                                            <th width="20%">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($applicants as $applicant): ?>
                                        <tr class="text-nowrap">
                                            <td class="align-middle"><?= esc($applicant['name']) ?></td>
                                            <td class="align-middle"><?= esc($applicant['age']) ?></td>
                                            <td class="align-middle">
                                                <?php if ($applicant['rate_qualification'] != 0): ?>
                                                    <span class="text-success"><i class="fas fa-check-circle"></i> Rated</span>
                                                <?php else: ?>
                                                    <span class="text-danger"><i class="fas fa-times-circle"></i> Not Rated</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="align-middle text-center"><?= $applicant['rate_total'] ?></td>
                                            <td class="align-middle"><?= esc($applicant['application_status']) ?></td>
                                            <td class="align-middle"><?= esc($applicant['app_status_reason']) ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <?php if ($applicant['application_status'] === 'Eliminated'): ?>
                                                        <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#undoEliminationModal<?= $applicant['id'] ?>">
                                                            <i class="fas fa-undo mr-1"></i> Undo Elimination
                                                        </button>
                                                    <?php else: ?>
                                                        <a href="<?= base_url("ratings/rateApplicant/{$applicant['id']}") ?>" class="btn btn-primary btn-sm">
                                                            Rate Applicant <i class="fas fa-star"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#eliminationModal<?= $applicant['id'] ?>">
                                                            <i class="fas fa-ban"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>

                                        <!-- Elimination Modal -->
                                        <div class="modal fade" id="eliminationModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="eliminationModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="eliminationModalLabel<?= $applicant['id'] ?>">
                                                            <i class="fas fa-ban mr-2"></i>
                                                            Eliminate Applicant
                                                        </h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <form action="<?= base_url("ratings/eliminate/{$applicant['id']}") ?>" method="post">
                                                        <div class="modal-body">
                                                            <p>Are you sure you want to eliminate <strong><?= esc($applicant['name']) ?></strong>?</p>
                                                            <div class="form-group">
                                                                <label for="elimination_reason<?= $applicant['id'] ?>">Reason for Elimination:</label>
                                                                <textarea class="form-control" id="elimination_reason<?= $applicant['id'] ?>" 
                                                                    name="eliminateReason" rows="3" required></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                                                <i class="fas fa-times mr-1"></i> Cancel
                                                            </button>
                                                            <button type="submit" class="btn btn-danger">
                                                                <i class="fas fa-ban mr-1"></i> Eliminate
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Undo Elimination Modal -->
                                        <div class="modal fade" id="undoEliminationModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="undoEliminationModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="undoEliminationModalLabel<?= $applicant['id'] ?>">
                                                            <i class="fas fa-undo mr-2"></i>
                                                            Undo Elimination
                                                        </h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <form action="<?= base_url("ratings/undoElimination/{$applicant['id']}") ?>" method="post">
                                                        <div class="modal-body">
                                                            <p>Are you sure you want to undo the elimination for <strong><?= esc($applicant['name']) ?></strong>?</p>
                                                            <div class="form-group">
                                                                <label for="undo_elimination_reason<?= $applicant['id'] ?>">Reason for Undoing Elimination:</label>
                                                                <textarea class="form-control" id="undo_elimination_reason<?= $applicant['id'] ?>" 
                                                                    name="undo_elimination_reason" rows="3" required></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                                                <i class="fas fa-times mr-1"></i> Cancel
                                                            </button>
                                                            <button type="submit" class="btn btn-warning">
                                                                <i class="fas fa-undo mr-1"></i> Undo Elimination
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
