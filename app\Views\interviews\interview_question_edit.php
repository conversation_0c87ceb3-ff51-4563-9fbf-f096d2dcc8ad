<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-edit mr-2"></i>
                        Edit Interview Question
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/open/' . $position['id']) ?>">Open Interview</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/questions/' . $question['position_id']) ?>">Questions</a></li>
                        <li class="breadcrumb-item active">Edit Question</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position: <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/questions/' . $question['position_id']) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Questions
                        </a>
                    </div>
                </div>
            </div>

            <!-- Edit Question Form -->
            <div class="card card-warning card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Question #<?= $question['question_no'] ?>
                    </h3>
                </div>
                <form method="POST" action="<?= base_url('interviews/questions/update/' . $question['id']) ?>">
                    <?= csrf_field() ?>
                    <div class="card-body">
                        <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="question_text">Question Text <span class="text-danger">*</span></label>
                                    <textarea class="form-control" 
                                              id="question_text" 
                                              name="question_text" 
                                              rows="4" 
                                              placeholder="Enter the interview question..."
                                              required><?= old('question_text', $question['question_text']) ?></textarea>
                                    <small class="form-text text-muted">
                                        Write a clear and specific question that will help evaluate the candidate's suitability for this position.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="set_score">Maximum Score <span class="text-danger">*</span></label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="set_score" 
                                           name="set_score" 
                                           min="1" 
                                           max="100" 
                                           value="<?= old('set_score', $question['set_score']) ?>" 
                                           required>
                                    <small class="form-text text-muted">
                                        Set the maximum points that can be awarded for this question (1-100).
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Question Information</label>
                                    <div class="alert alert-info">
                                        <strong>Question Number:</strong> <?= $question['question_no'] ?><br>
                                        <strong>Created:</strong> <?= date('M d, Y \a\t h:i A', strtotime($question['created_at'])) ?><br>
                                        <?php if ($question['updated_at'] && $question['updated_at'] != $question['created_at']): ?>
                                            <strong>Last Updated:</strong> <?= date('M d, Y \a\t h:i A', strtotime($question['updated_at'])) ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Original vs Current Comparison -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card card-light">
                                    <div class="card-header">
                                        <h6 class="card-title">
                                            <i class="fas fa-history mr-1"></i>
                                            Original Question
                                        </h6>
                                        <div class="card-tools">
                                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <h6>Original Question Text:</h6>
                                                <div class="alert alert-light">
                                                    <?= esc($question['question_text']) ?>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <h6>Original Score:</h6>
                                                <div class="alert alert-light">
                                                    <span class="badge badge-warning badge-lg">
                                                        <?= $question['set_score'] ?> points
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save mr-1"></i>
                                    Update Question
                                </button>
                                <a href="<?= base_url('interviews/questions/' . $question['position_id']) ?>" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times mr-1"></i>
                                    Cancel
                                </a>
                            </div>
                            <div class="col-md-6 text-right">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    All fields marked with <span class="text-danger">*</span> are required
                                </small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Auto-resize textarea
    $('#question_text').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Initial resize
    $('#question_text').trigger('input');

    // Form validation
    $('form').on('submit', function(e) {
        var questionText = $('#question_text').val().trim();
        var setScore = $('#set_score').val();

        if (questionText.length < 10) {
            e.preventDefault();
            alert('Question text must be at least 10 characters long.');
            $('#question_text').focus();
            return false;
        }

        if (setScore < 1 || setScore > 100) {
            e.preventDefault();
            alert('Maximum score must be between 1 and 100.');
            $('#set_score').focus();
            return false;
        }

        // Confirm update
        if (!confirm('Are you sure you want to update this question?')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
<?= $this->endSection() ?>
