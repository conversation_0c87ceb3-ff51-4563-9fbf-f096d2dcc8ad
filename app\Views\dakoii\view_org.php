<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<body>
    <div class="container-fluid p-2">
        <div class="row">
            <div class="col-md-12">
                <h4>View Organization: <?= esc($org['name']) ?></h4>
                <a href="<?= base_url('dakoii/dashboard') ?>" class="btn btn-secondary mb-3">Back to Dashboard</a>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title"><?= esc($org['name']) ?> (<?= esc($org['orgcode']) ?>)</h5>
                        <p class="card-text"><strong>Description:</strong> <?= esc($org['description']) ?></p>
                        <p class="card-text"><strong>Status:</strong> <?= $org['is_active'] ? 'Active' : 'Inactive' ?></p>
                        <p class="card-text"><strong>License Status:</strong> <?= esc($org['license_status']) ?></p>
                        <p class="card-text"><strong>Created At:</strong> <?= $org['created_at'] ?></p>
                        <p class="card-text"><strong>Updated At:</strong> <?= $org['updated_at'] ?></p>
                    </div>
                </div>

                <h5>Users</h5>
                <button type="button" class="btn btn-primary mb-3" data-toggle="modal" data-target="#createUserModal">
                    Create New User
                </button>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Username</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $index => $user) : ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td><?= esc($user['name']) ?></td>
                                <td><?= esc($user['username']) ?></td>
                                <td><?= esc($user['role']) ?></td>
                                <td><?= $user['is_active'] ? 'Active' : 'Inactive' ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#editUserModal<?= $user['id'] ?>">
                                        Edit
                                    </button>
                                    <a href="<?= base_url('dakoii/deleteUser/' . $user['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this user?')">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1" role="dialog" aria-labelledby="createUserModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createUserModalLabel">Create New User</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="<?= base_url('dakoii/createUser/' . $org['id']) ?>" method="post">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label for="role">Role</label>
                            <select class="form-control" id="role" name="role" required>
                                <option value="user">User</option>
                                <option value="moderator">Moderator</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="is_active">Active</label>
                            <input type="checkbox" id="is_active" name="is_active" value="1">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Create User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modals -->
    <?php foreach ($users as $user) : ?>
        <div class="modal fade" id="editUserModal<?= $user['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel<?= $user['id'] ?>" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editUserModalLabel<?= $user['id'] ?>">Edit User</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form action="<?= base_url('dakoii/updateUser/' . $user['id']) ?>" method="post">
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="name<?= $user['id'] ?>">Name</label>
                                <input type="text" class="form-control" id="name<?= $user['id'] ?>" name="name" value="<?= esc($user['name']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="username<?= $user['id'] ?>">Username</label>
                                <input type="text" class="form-control" id="username<?= $user['id'] ?>" name="username" value="<?= esc($user['username']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="password<?= $user['id'] ?>">Password (leave blank to keep current)</label>
                                <input type="password" class="form-control" id="password<?= $user['id'] ?>" name="password">
                            </div>
                            <div class="form-group">
                                <label for="role<?= $user['id'] ?>">Role</label>
                                <select class="form-control" id="role<?= $user['id'] ?>" name="role" required>
                                    <option value="user" <?= $user['role'] == 'user' ? 'selected' : '' ?>>User</option>
                                    <option value="moderator" <?= $user['role'] == 'moderator' ? 'selected' : '' ?>>Moderator</option>
                                    <option value="admin" <?= $user['role'] == 'admin' ? 'selected' : '' ?>>Admin</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="is_active<?= $user['id'] ?>">Active</label>
                                <input type="checkbox" id="is_active<?= $user['id'] ?>" name="is_active" value="1" <?= $user['is_active'] ? 'checked' : '' ?>>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary">Update User</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
</body>

<?= $this->endSection() ?>