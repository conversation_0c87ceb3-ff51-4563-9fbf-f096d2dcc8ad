<?php

namespace App\Controllers;

use App\Models\countryModel;
use App\Models\dakoiiUsersModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\orgModel;
use App\Models\provinceModel;
use App\Models\usersModel;
use App\Models\wardModel;

class Users extends BaseController
{
    public $session;
    public $dakoii_usersModel;
    public $usersModel;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $wardModel;


    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dakoii_usersModel = new dakoiiUsersModel();
        $this->usersModel = new usersModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->wardModel = new wardModel();
    }

    public function index()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dakoii_login";
    }

    //create country users
    public function create_user()
    {
        $email    = $this->request->getVar('email');
        
        //check if username exist
        $users = $this->usersModel->where('email', $email)->first();

        if (!empty($users)) {
            return redirect()->back()->with("error", "Email Already Existed");
        }
        
        
        //get id_photo
        $file = $this->request->getFile('id_photo');

        $filepath = null;
        if (!empty($file)) {
            if ($file->isValid() && !$file->hasMoved()) {
                $newName = "id_" . $file->getRandomName();
                $file->move(WRITEPATH . '../public/uploads/employees/', $newName);

                $filepath = "/public/uploads/employees/" . $newName;
            }
        }

        //set ucode
        $ucode = uniqid() . time();

        // Retrieve form data
        $data = [
            'ucode'       => $ucode,
            'national_id' => session('national_id'),
            'org_id'    => session('org_id'),
            'org_level'    => session('org_level'),
            'fname'       => $this->request->getVar('fname'),
            'lname'       => $this->request->getVar('lname'),
            'gender'      => $this->request->getVar('gender'),
            'dobirth'     => $this->request->getVar('dobirth'),
            'place_birth' => $this->request->getVar('place_birth'),
            'joined_date' => $this->request->getVar('joined_date'),
            'role'        => $this->request->getVar('user_role'),
            'password'    => password_hash('123abc', PASSWORD_DEFAULT), // Hash the password
            'phone'       => $this->request->getVar('phone'),
            'email'       => $email,
            'address'     => $this->request->getVar('address'),
            'designation' => $this->request->getVar('designation'),
            'sector'      => $this->request->getVar('sector'),
            'supervisor_id'  => $this->request->getVar('supervisor_id'),
            'branch_id'    => $this->request->getVar('branch_id'),
            'id_photo'    => $filepath,
            'type'        => $this->request->getVar('user_type'),
            'is_active'   => 1, // Default to active user
            'create_by'   => session('user_email'), // Assuming the creator's user ID is 1 (this should be dynamic based on logged-in user)
            //'update_by'   => , // Assuming the updater's user ID is 1 (this should be dynamic based on logged-in user)
        ];

        // Insert data into the database
        if ($this->usersModel->insert($data)) {

            //email user
            sendmail($email, "AMIS Account", "Your AMIS Account has been created successfully. Please use the following credentials to login: Email: " . $email . " Password: " . '123abc');
            
            // Redirect or return success message
            return redirect()->back()->with('success', 'User created successfully');
        } else {
            // Handle the error
            return redirect()->back()->with('error', 'Failed to create user.');
        }
    }

    //check username

    public function checkUsername()
    {
        $email = $this->request->getPost('username');

        $isTaken = $this->usersModel->where('email', $email)->first();

        if (!empty($isTaken)) {
            return $this->response->setJSON(['error' => 'Email Already Existed']);
        }
    }


    //update users
    public function update_user()
    {
        $id = $this->request->getVar('id');

        // Retrieve form data
        $data = [
            'fname'       => $this->request->getVar('fname'),
            'lname'       => $this->request->getVar('lname'),
            'gender'      => $this->request->getVar('gender'),
            'dobirth'     => $this->request->getVar('dobirth'),
            'place_birth' => $this->request->getVar('place_birth'),
            'joined_date' => $this->request->getVar('joined_date'),
            'role'        => $this->request->getVar('user_role'),
            'phone'       => $this->request->getVar('phone'),
            'address'     => $this->request->getVar('address'),
            'designation' => $this->request->getVar('designation'),
            'supervisor_id'  => $this->request->getVar('supervisor_id'),
            'sector'      => $this->request->getVar('sector'),
            'branch_id'    => $this->request->getVar('branch_id'),
            
            /*             'previledge'  => $this->request->getVar('user_previledge'), */
            'is_active'   => $this->request->getVar('status'),
            'update_by'   => session('user_email'), // Assuming the creator's user ID is 1 (this should be dynamic based on logged-in user)
            //'update_by'   => , // Assuming the updater's user ID is 1 (this should be dynamic based on logged-in user)
        ];

        // Insert data into the database
        if ($this->usersModel->update($id, $data)) {
            // Redirect or return success message
            return redirect()->back()->with('success', 'Changes Saved');
        } else {
            // Handle the error
            return redirect()->back()->with('error', 'Changes Failed.');
        }
    }


    //update country users
    public function update_user_password()
    {
        $id = $this->request->getVar('id');
        
        //get user email
        $user = $this->usersModel->where('id', $id)->first();
        
        // Retrieve form data
        $data = [
            'password'    => password_hash($this->request->getVar('password'), PASSWORD_DEFAULT), // Hash the password
            'update_by'   => session('user_email'), // Assuming the creator's user ID is 1 (this should be dynamic based on logged-in user)
            //'update_by'   => , // Assuming the updater's user ID is 1 (this should be dynamic based on logged-in user)
        ];

        // Insert data into the database
        if ($this->usersModel->update($id, $data)) {
            
            sendmail($user['email'], "Password Reset", "Your password has been reset to: " . $this->request->getVar('password'));
            
            // Redirect or return success message
            return redirect()->back()->with('success', 'Password Reseted');
        } else {
            // Handle the error
            return redirect()->back()->with('error', 'Changes Failed.');
        }
    }
    

    //============================ My Users ===============

    //view_my_users
    public function view_my_users()
    {
        $data['title'] = "My Users";
        $data['menu'] = "my_users";

        //get users of this organization
        $data['users'] = $this->usersModel->where('group_id', session('org_id'))->where('type', session('org_type'))->where('level', session('org_level'))->find();

        return view('users/view_my_users', $data);
    }

    //view employees
    public function hr_view_employees()
    {
        $data['title'] = "Employees";
        $data['menu'] = "hr";

        $data['employees'] = $this->usersModel->where('org_level', session('org_level'))->where('org_id', session('org_id'))->find();

        //get data['employees'] id into an array
        $group_levels = $group_ids = [];
        foreach ($data['employees'] as $emp) {
            array_push($group_ids, $emp['station_id']);
            array_push($group_levels, $emp['level']);
        }

        $data['wards'] = $data['llgs'] = $data['districts'] = $data['provinces'] = $data['countries'] = [];
        //if group_level is national get countryModel data['countries']
        if (in_array("national", $group_levels)) {
            $level_data = $this->countryModel->whereIn('id', $group_ids)->find();

            $data['countries'] = array_map(function ($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['name']
                ];
            }, $level_data);
        }
        //if group_level is province get provinceModel data['provinces']
        if (in_array("province", $group_levels)) {
            $level_data = $this->provinceModel->whereIn('id', $group_ids)->find();

            $data['provinces'] = array_map(function ($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['province_name']
                ];
            }, $level_data);
        }
        //if group_level is district get districtModel data['districts']
        if (in_array("district", $group_levels)) {
            $level_data = $this->districtModel->whereIn('id', $group_ids)->find();

            $data['districts'] = array_map(function ($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['district_name']
                ];
            }, $level_data);
        }
        //if group_level is llg get llgModel data['llgs']
        if (in_array("llg", $group_levels)) {
            $level_data = $this->llgModel->whereIn('id', $group_ids)->find();

            $data['llgs'] = array_map(function ($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['llg_name']
                ];
            }, $level_data);
        }
        //if group_level is ward get wardModel data['wards']
        if (in_array("ward", $group_levels)) {
            $level_data = $this->wardModel->whereIn('id', $group_ids)->find();

            $data['wards'] = array_map(function ($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['ward_name']
                ];
            }, $level_data);
        }

        echo view('users/hr_view_employees', $data);
    }


    public function hr_view_emp_profile($ucode)
    {

        $data['menu'] = "hr";
        $data['submenu'] = "employees";

        $data['user'] = $this->usersModel->where('ucode', $ucode)->first();

        // print_r($data['user']);

        $data['station'] = [];

        if (!empty($data['user'])) {

            if ($data['user']['level'] == 'national') {
                $country = $this->countryModel->where('id', $data['user']['station_id'])->first();
                if ($country) {
                    $data['station'] = [
                        'id' => $country['id'],
                        'name' => $country['name']
                    ];
                } else {
                    $data['station'] = [
                        'id' => "",
                        'name' => ""
                    ];
                }
            }

            if ($data['user']['level'] == 'province') {
                $province = $this->provinceModel->where('id', $data['user']['station_id'])->first();
                if ($province) {
                    $data['station'] = [
                        'id' => $province['id'],
                        'name' => $province['province_name']
                    ];
                } else {
                    $data['station'] = [
                        'id' => "",
                        'name' => ""
                    ];
                }
            }

            if ($data['user']['level'] == 'district') {
                $district = $this->districtModel->where('id', $data['user']['station_id'])->first();
                if ($district) {
                    $data['station'] = [
                        'id' => $district['id'],
                        'name' => $district['district_name']
                    ];
                } else {
                    $data['station'] = [
                        'id' => "",
                        'name' => ""
                    ];
                }
            }

            if ($data['user']['level'] == 'llg') {
                $llg = $this->llgModel->where('id', $data['user']['station_id'])->first();
                if ($llg) {
                    $data['station'] = [
                        'id' => $llg['id'],
                        'name' => $llg['llg_name']
                    ];
                } else {
                    $data['station'] = [
                        'id' => "",
                        'name' => ""
                    ];
                }
            }

            if ($data['user']['level'] == 'ward') {
                $ward = $this->wardModel->where('id', $data['user']['station_id'])->first();
                if ($ward) {
                    $data['station'] = [
                        'id' => $ward['id'],
                        'name' => $ward['ward_name']
                    ];
                } else {
                    $data['station'] = [
                        'id' => "",
                        'name' => ""
                    ];
                }
            }
        }

        $data['title'] = 'Edit:' . $data['user']['username'];
        echo view('users/hr_view_emp_profile', $data);
    }
}
