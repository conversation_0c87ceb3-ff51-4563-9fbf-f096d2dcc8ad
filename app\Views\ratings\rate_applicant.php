<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Rate Applicant: <?= esc($applicant['name']) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('ratings') ?>">Ratings</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("ratings/viewPositions/{$position['position_group_id']}") ?>">View Positions</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("ratings/viewApplicants/{$position['id']}") ?>">View Applicants</a></li>
                        <li class="breadcrumb-item active">Rate Applicant</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">

            <div class="row">
                <div class="col-12 p-3">
                    <button type="button" class="btn bg-purple float-right btn-copy-details" onclick="copyAllDetails()">
                        <i class="fas fa-copy"></i> AI Prompt
                    </button>
                </div>
                <script>
                    function copyAllDetails() {
                        const positionDiv = document.getElementById('position-details');
                        const applicantDiv = document.getElementById('applicant-details');
                        const rateDiv = document.getElementById('rate-applicant-form');

                        const text = "INSTRUCTIONS:\n" +
                            "Analyze the applicant specifications against the position specifications and give a rating score out of 61 for this applicant for each of the following criteria:\n\n" +
                            "Write informative analysis remarks stating the facts and figures for each rating.\n\n" +
                            "Start off analysis write up with this: This is the rating analysis for the applicant: <?= esc($applicant['name']) ?> against the position: <?= esc($position['designation']) ?> \n\n " +
                            "RATING CRITERIA:\n" +
                            "================\n" +
                            "1. Age (1 point if age is not given or is 0) (points: 0-8) \n" +
                            "2. Education Qualification (1-10 points) \n" +
                            "3. Capability (1-5 points) \n" +
                            "4. Public Servant Status (1-3 points) \n" +
                            "5. Skills and Competencies (1-5 points) \n" +
                            "6. Work Experience/Employment History:\n" +
                            "7. Private Sector (Non-Relevant) (0-4 points) \n" +
                            "8. Private Sector (Relevant) (0-5 points) \n" +
                            "9. Public Service (Non-Relevant) (0-5 points) \n" +
                            "10. Public Service (Relevant) (0,2-6 points) \n" +


                            "11. Knowledge (0-5 points) \n" +
                            "12. Training Relevance (0-5 points) \n\n" +
                            "Please provide brief remarks after rating.\n\n" +
                            "POSITION DETAILS:\n" +
                            "================\n" +
                            positionDiv.innerText +
                            "\n\nAPPLICANT DETAILS:\n" +


                            "================\n" +
                            applicantDiv.innerText +
                            "\n\nRATING FORM:\n" +
                            "================\n" +
                            rateDiv.innerText;

                        navigator.clipboard.writeText(text).then(() => {
                            // Show success feedback
                            const btn = document.querySelector('.btn-copy-details');
                            const originalText = btn.innerHTML;
                            btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                            setTimeout(() => {
                                btn.innerHTML = originalText;
                            }, 2000);
                        }).catch(err => {
                            console.error('Failed to copy text: ', err);
                        });
                    }
                </script>

            </div>

            <div class="row">
                <div class="col-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-briefcase mr-2"></i>
                                Position Specifications
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="position-details">
                                <h4><?= esc($position['designation']) ?></h4>
                                <p><strong>Position No.:</strong> <?= esc($position['position_no']) ?></p>
                                <p><strong>Classification:</strong> <?= esc($position['classification']) ?></p>
                                <p><strong>Qualifications:</strong> <?= nl2br(esc($position['qualifications'])) ?></p>
                                <p><strong>Knowledge:</strong> <?= nl2br(esc($position['knowledge'])) ?></p>
                                <p><strong>Skills and Competencies:</strong> <?= nl2br(esc($position['skills_competencies'])) ?></p>
                                <p><strong>Job Experiences:</strong> <?= nl2br(esc($position['job_experiences'])) ?></p>
                            </div>
                        </div>
                        <script>
                            function copyPositionDetails() {
                                const detailsDiv = document.getElementById('position-details');
                                const text = detailsDiv.innerText;

                                navigator.clipboard.writeText(text).then(() => {
                                    // Show success feedback
                                    const btn = document.querySelector('.copy-position-details-btn');
                                    const originalText = btn.innerHTML;
                                    btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                                    setTimeout(() => {
                                        btn.innerHTML = originalText;
                                    }, 2000);
                                }).catch(err => {
                                    console.error('Failed to copy text: ', err);
                                });
                            }
                        </script>
                    </div>
                </div>

                <div class="col-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user mr-2"></i>
                                Applicant Specifications
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="applicant-details">
                                <h4><?= esc($applicant['name']) ?></h4>
                                <p><strong>Sex:</strong> <?= esc($applicant['sex']) ?></p>
                                <p><strong>Age:</strong> <?= esc($applicant['age']) ?></p>
                                <p><strong>Place of Origin:</strong> <?= esc($applicant['place_origin']) ?></p>
                                <p><strong>Current Employer:</strong> <?= esc($applicant['current_employer']) ?></p>
                                <p><strong>Current Position:</strong> <?= esc($applicant['current_position']) ?></p>
                                <p><strong>Address/Location:</strong> <?= esc($applicant['address_location']) ?></p>
                                <p><strong>Contact Details:</strong> <?= esc($applicant['contact_details']) ?></p>
                                <p><strong>NID Number:</strong> <?= esc($applicant['nid_number']) ?></p>
                                <p><strong>Qualification:</strong> <?= nl2br(esc($applicant['qualification_text'])) ?></p>
                                <p><strong>Other Trainings:</strong> <?= nl2br(esc($applicant['other_trainings'])) ?></p>
                                <p><strong>Knowledge:</strong> <?= nl2br(esc($applicant['knowledge'])) ?></p>
                                <p><strong>Skills and Competencies:</strong> <?= nl2br(esc($applicant['skills_competencies'])) ?></p>
                                <p><strong>Job Experiences:</strong> <?= nl2br(esc($applicant['job_experiences'])) ?></p>
                                <p><strong>Publications:</strong> <?= nl2br(esc($applicant['publications'])) ?></p>
                                <p><strong>Awards:</strong> <?= nl2br(esc($applicant['awards'])) ?></p>
                                <p><strong>Referees:</strong> <?= nl2br(esc($applicant['referees'])) ?></p>
                                <p><strong>Comments:</strong> <?= nl2br(esc($applicant['comments'])) ?></p>
                            </div>
                        </div>
                        <script>
                            function copyApplicantDetails() {
                                const detailsDiv = document.getElementById('applicant-details');
                                const text = detailsDiv.innerText;

                                navigator.clipboard.writeText(text).then(() => {
                                    // Show success feedback
                                    const btn = document.querySelector('.copy-applicant-details-btn');
                                    const originalText = btn.innerHTML;
                                    btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                                    setTimeout(() => {
                                        btn.innerHTML = originalText;
                                    }, 2000);
                                }).catch(err => {
                                    console.error('Failed to copy text: ', err);
                                });
                            }
                        </script>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-star mr-2"></i>
                                Rate Applicant
                            </h3>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url("ratings/rateApplicant/{$applicant['id']}") ?>" method="post">
                                <div class="row" id="rate-applicant-form">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="rate_age"><strong>Age</strong></label>
                                            <select class="form-control " id="rate_age" name="rate_age" required>
                                                <option value="">Select Age Range</option>
                                                <option value="0" <?= ($applicant['rate_age'] == '0') ? 'selected' : '' ?>>0 | 65+yrs </option>
                                                <option value="1" <?= ($applicant['rate_age'] == '1') ? 'selected' : '' ?>>1 | 60-64yrs </option>
                                                <option value="2" <?= ($applicant['rate_age'] == '2') ? 'selected' : '' ?>>2 | 54-59yrs </option>
                                                <option value="3" <?= ($applicant['rate_age'] == '3') ? 'selected' : '' ?>>3 | 48-53yrs </option>
                                                <option value="4" <?= ($applicant['rate_age'] == '4') ? 'selected' : '' ?>>4 | 42-47yrs </option>
                                                <option value="5" <?= ($applicant['rate_age'] == '5') ? 'selected' : '' ?>>5 | 36-41yrs </option>
                                                <option value="6" <?= ($applicant['rate_age'] == '6') ? 'selected' : '' ?>>6 | 30-35yrs </option>
                                                <option value="7" <?= ($applicant['rate_age'] == '7') ? 'selected' : '' ?>>7 | 24-29yrs </option>
                                                <option value="8" <?= ($applicant['rate_age'] == '8') ? 'selected' : '' ?>>8 | 18-23yrs </option>

                                            </select>
                                            <input type="hidden" name="max_rate_age" value="8">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_education"><strong>Education Qualification</strong></label>
                                            <select class="form-control " id="rate_education" name="rate_education" required>
                                                <option value="">Select Education Range</option>
                                                <option value="10" <?= ($applicant['rate_qualification'] == '10') ? 'selected' : '' ?>>10 | Doctorate</option>
                                                <option value="9" <?= ($applicant['rate_qualification'] == '9') ? 'selected' : '' ?>>9 | Master</option>

                                                <option value="8" <?= ($applicant['rate_qualification'] == '8') ? 'selected' : '' ?>>8 | Honors/Post.Grad/Cert/Diploma</option>
                                                <option value="7" <?= ($applicant['rate_qualification'] == '7') ? 'selected' : '' ?>>7 | Bachelors Degree</option>

                                                <option value="6" <?= ($applicant['rate_qualification'] == '6') ? 'selected' : '' ?>>6 | Adv.Diploma/Associate Degree</option>
                                                <option value="5" <?= ($applicant['rate_qualification'] == '5') ? 'selected' : '' ?>>5 | Diploma</option>

                                                <option value="4" <?= ($applicant['rate_qualification'] == '4') ? 'selected' : '' ?>>4 | Cert.4/Certificate (High.Edu) </option>
                                                <option value="3" <?= ($applicant['rate_qualification'] == '3') ? 'selected' : '' ?>>3 | Cert.3 </option>

                                                <option value="2" <?= ($applicant['rate_qualification'] == '2') ? 'selected' : '' ?>>2 | Cert.2 </option>

                                                <option value="1" <?= ($applicant['rate_qualification'] == '1') ? 'selected' : '' ?>>1 | Cert.1 </option>
                                            </select>
                                            <input type="hidden" name="max_rate_education" value="10">

                                        </div>
                                        <div class="form-group">
                                            <label for="rate_capability"><strong>Capability Rating</strong></label>
                                            <select class="form-control " id="rate_capability" name="rate_capability" required>
                                                <option value="">Select Capability Level</option>
                                                <option value="5" <?= ($applicant['rate_capability'] == '5') ? 'selected' : '' ?>>5 | Very Capable </option>

                                                <option value="4" <?= ($applicant['rate_capability'] == '4') ? 'selected' : '' ?>>4 | Above Average </option>
                                                <option value="3" <?= ($applicant['rate_capability'] == '3') ? 'selected' : '' ?>>3 | Average/Competent </option>

                                                <option value="2" <?= ($applicant['rate_capability'] == '2') ? 'selected' : '' ?>>2 | Low Capability </option>

                                                <option value="1" <?= ($applicant['rate_capability'] == '1') ? 'selected' : '' ?>>1 | No Capability </option>
                                            </select>
                                            <input type="hidden" name="max_rate_capability" value="5">
                                        </div>

                                        <div class="form-group">
                                            <label for="rate_public_service"><strong>Public Service Status</strong></label>
                                            <select class="form-control " id="rate_public_service" name="rate_public_service" required>
                                                <option value="">Select Status</option>
                                                <option value="3" <?= ($applicant['rate_public_service'] == '3') ? 'selected' : '' ?>>3 | Section 39 </option>
                                                <option value="2" <?= ($applicant['rate_public_service'] == '2') ? 'selected' : '' ?>>2 | Public Servant </option>

                                                <option value="1" <?= ($applicant['rate_public_service'] == '1') ? 'selected' : '' ?>>1 | Non-Public Servant </option>
                                            </select>
                                            <input type="hidden" name="max_rate_public_service" value="3">

                                        </div>
                                        <div class="form-group">
                                            <label for="rate_skills_competencies"><strong>Skills and Competencies Rating</strong></label>
                                            <select class="form-control " id="rate_skills_competencies" name="rate_skills_competencies" required>
                                                <option value="">Select Skills and Competencies Level</option>
                                                <option value="5" <?= ($applicant['rate_skills_competencies'] == '5') ? 'selected' : '' ?>>5 | Exceptional Skills </option>

                                                <option value="4" <?= ($applicant['rate_skills_competencies'] == '4') ? 'selected' : '' ?>>4 | Above Average Skills </option>
                                                <option value="3" <?= ($applicant['rate_skills_competencies'] == '3') ? 'selected' : '' ?>>3 | Average Skills </option>

                                                <option value="2" <?= ($applicant['rate_skills_competencies'] == '2') ? 'selected' : '' ?>>2 | Below Average Skills </option>

                                                <option value="1" <?= ($applicant['rate_skills_competencies'] == '1') ? 'selected' : '' ?>>1 | Limited Skills </option>
                                                <option value="0" <?= ($applicant['rate_skills_competencies'] == '0') ? 'selected' : '' ?>>0 | No Skills </option>
                                            </select>
                                            <input type="hidden" name="max_rate_skills_competencies" value="5">

                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="rate_private_non_relevant"><strong>Private Sector Work Experience (Non-Relevant)</strong></label>
                                            <select class="form-control" id="rate_private_non_relevant" name="rate_private_non_relevant" required>
                                                <option value="">Select Prv. NON-RELEVANT Experience</option>
                                                <option value="4" <?= ($applicant['rate_private_non_relevant'] == '4') ? 'selected' : '' ?>>4 | Prv. Non-Relevant 20+ yrs </option>
                                                <option value="3" <?= ($applicant['rate_private_non_relevant'] == '3') ? 'selected' : '' ?>>3 | Prv. Non-Relevant 15-19yrs </option>

                                                <option value="2" <?= ($applicant['rate_private_non_relevant'] == '2') ? 'selected' : '' ?>>2 | Prv. Non-Relevant 10-14yrs </option>
                                                <option value="1" <?= ($applicant['rate_private_non_relevant'] == '1') ? 'selected' : '' ?>>1 | Prv. Non-Relevant 5-9yrs </option>
                                                <option value="0" <?= ($applicant['rate_private_non_relevant'] == '0') ? 'selected' : '' ?>>0 | No/Less Non-Relevant Xp. 0-4yrs </option>

                                            </select>
                                            <input type="hidden" name="max_rate_private_non_relevant" value="4">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_private_relevant"><strong>Private Sector Work Experience (Relevant)</strong></label>
                                            <select class="form-control " id="rate_private_relevant" name="rate_private_relevant" required>
                                                <option value="">Select Prv. RELEVANT Experience</option>
                                                <option value="5" <?= ($applicant['rate_private_relevant'] == '5') ? 'selected' : '' ?>>5 | Prv. Relevant 20+ yrs </option>

                                                <option value="4" <?= ($applicant['rate_private_relevant'] == '4') ? 'selected' : '' ?>>4 | Prv. Relevant 15-19yrs </option>
                                                <option value="3" <?= ($applicant['rate_private_relevant'] == '3') ? 'selected' : '' ?>>3 | Prv. Relevant 10-14yrs </option>

                                                <option value="2" <?= ($applicant['rate_private_relevant'] == '2') ? 'selected' : '' ?>>2 | Prv. Relevant 5-9yrs </option>

                                                <option value="1" <?= ($applicant['rate_private_relevant'] == '1') ? 'selected' : '' ?>>1 | Prv. Relevant 1-4yrs </option>
                                                <option value="0" <?= ($applicant['rate_private_relevant'] == '0') ? 'selected' : '' ?>>0 | Prv. No Relevant Xp. </option>
                                            </select>

                                            <input type="hidden" name="max_rate_private_relevant" value="5">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_public_non_relevant"><strong>P.S Non-Relevant Work Experience</strong></label>
                                            <select class="form-control " id="rate_public_non_relevant" name="rate_public_non_relevant" required>
                                                <option value="">Select PS. NON-RELEVANT Experience</option>
                                                <option value="5" <?= ($applicant['rate_public_non_relevant'] == '5') ? 'selected' : '' ?>>5 | PS. Non-Relevant 20+ yrs </option>
                                                <option value="4" <?= ($applicant['rate_public_non_relevant'] == '4') ? 'selected' : '' ?>>4 | PS. Non-Relevant 15-19yrs </option>

                                                <option value="3" <?= ($applicant['rate_public_non_relevant'] == '3') ? 'selected' : '' ?>>3 | PS. Non-Relevant 10-14yrs </option>
                                                <option value="2" <?= ($applicant['rate_public_non_relevant'] == '2') ? 'selected' : '' ?>>2 | PS. Non-Relevant 5-9yrs </option>

                                                <option value="1" <?= ($applicant['rate_public_non_relevant'] == '1') ? 'selected' : '' ?>>1 | PS. Non-Relevant 1-4yrs </option>
                                                <option value="0" <?= ($applicant['rate_public_non_relevant'] == '0') ? 'selected' : '' ?>>0 | PS. No Experience </option>
                                            </select>

                                            <input type="hidden" name="max_rate_public_non_relevant" value="5">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_public_relevant"><strong>P.S Relevant Work Experience</strong></label>
                                            <select class="form-control" id="rate_public_relevant" name="rate_public_relevant" required>
                                                <option value="">Select PS. RELEVANT Experience</option>
                                                <option value="6" <?= ($applicant['rate_public_relevant'] == '6') ? 'selected' : '' ?>>6 | PS. Relevant 20+ yrs </option>
                                                <option value="5" <?= ($applicant['rate_public_relevant'] == '5') ? 'selected' : '' ?>>5 | PS. Relevant 15-19yrs </option>

                                                <option value="4" <?= ($applicant['rate_public_relevant'] == '4') ? 'selected' : '' ?>>4 | PS. Relevant 10-14yrs </option>
                                                <option value="3" <?= ($applicant['rate_public_relevant'] == '3') ? 'selected' : '' ?>>3 | PS. Relevant 5-9yrs </option>

                                                <option value="2" <?= ($applicant['rate_public_relevant'] == '2') ? 'selected' : '' ?>>2 | PS. Relevant 1-4yrs </option>
                                                <option value="0" <?= ($applicant['rate_public_relevant'] == '0') ? 'selected' : '' ?>>0 | PS. No Experience </option>
                                            </select>
                                            <input type="hidden" name="max_rate_public_relevant" value="6">

                                        </div>
                                        <div class="form-group">
                                            <label for="rate_knowledge"><strong>Knowledge Rating</strong></label>
                                            <select class="form-control " id="rate_knowledge" name="rate_knowledge" required>
                                                <option value="">Select Knowledge Level</option>
                                                <option value="5" <?= ($applicant['rate_knowledge'] == '5') ? 'selected' : '' ?>>5 | Expert Knowledge </option>
                                                <option value="4" <?= ($applicant['rate_knowledge'] == '4') ? 'selected' : '' ?>>4 | Advanced Knowledge </option>

                                                <option value="3" <?= ($applicant['rate_knowledge'] == '3') ? 'selected' : '' ?>>3 | Intermediate Knowledge </option>
                                                <option value="2" <?= ($applicant['rate_knowledge'] == '2') ? 'selected' : '' ?>>2 | Basic Knowledge </option>
                                                <option value="1" <?= ($applicant['rate_knowledge'] == '1') ? 'selected' : '' ?>>1 | Limited Knowledge </option>

                                                <option value="0" <?= ($applicant['rate_knowledge'] == '0') ? 'selected' : '' ?>>0 | No Knowledge </option>
                                            </select>
                                            <input type="hidden" name="max_rate_knowledge" value="5">

                                        </div>
                                        <div class="form-group">
                                            <label for="rate_trainings"><strong>Training Relevance Rating</strong></label>
                                            <select class="form-control " id="rate_trainings" name="rate_trainings" required data-placeholder="Select Training Relevance">
                                                <option value="">Select Training Relevance</option>
                                                <option value="5" <?= ($applicant['rate_trainings'] == '5') ? 'selected' : '' ?>>5 | Highly relevant to the position </option>
                                                <option value="4" <?= ($applicant['rate_trainings'] == '4') ? 'selected' : '' ?>>4 | Very relevant to the position </option>
                                                <option value="3" <?= ($applicant['rate_trainings'] == '3') ? 'selected' : '' ?>>3 | Moderately relevant to the position </option>
                                                <option value="2" <?= ($applicant['rate_trainings'] == '2') ? 'selected' : '' ?>>2 | Somewhat relevant to the position </option>
                                                <option value="1" <?= ($applicant['rate_trainings'] == '1') ? 'selected' : '' ?>>1 | Slightly relevant to the position </option>
                                                <option value="0" <?= ($applicant['rate_trainings'] == '0') ? 'selected' : '' ?>>0 | Not relevant to the position </option>
                                            </select>
                                            <input type="hidden" name="max_rate_trainings" value="5">
                                        </div>


                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="remarks">Remarks</label>
                                    <textarea class="form-control" id="remarks" name="remarks" rows="5" placeholder="Write Remarks"><?= $applicant['remarks'] ?? '' ?></textarea>
                                </div>
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">Submit Rating</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="card card-primary card-outline mt-3">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-star mr-2"></i>
                                AI Rating Assistant
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="ai-chat-container" class="chat-container" style="height: 500px; overflow-y: auto;">
                                <!-- Chat messages will appear here -->
                            </div>
                            <div class="input-group mt-3">
                                <button type="button" class="btn bg-purple" id="analyzeButton">
                                    <i class="fas fa-robot mr-1"></i> Analyze Applicant
                                </button>
                            </div>
                        </div>
                    </div>




                </div>

            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-bar mr-2"></i>
                                Rating Summary
                            </h3>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Criteria</th>
                                        <th>Rating</th>
                                        <th>Out of</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Age</td>
                                        <td><?= $applicant['rate_age'] ?? 0 ?></td>
                                        <td><?= $applicant['max_rate_age'] ?? 0 ?></td>
                                    </tr>
                                    <tr>
                                        <td>Qualification</td>
                                        <td><?= $applicant['rate_qualification'] ?? 0 ?></td>
                                        <td><?= $applicant['max_rate_qualification'] ?? 0 ?></td>
                                    </tr>
                                    <tr>
                                        <td>Experience</td>
                                        <td>
                                            <?=
                                            ($applicant['rate_private_non_relevant'] ?? 0) +
                                                ($applicant['rate_private_relevant'] ?? 0) +
                                                ($applicant['rate_public_non_relevant'] ?? 0) +
                                                ($applicant['rate_public_relevant'] ?? 0) ?>
                                        </td>
                                        <td>
                                            <?=
                                            ($applicant['max_rate_private_non_relevant'] ?? 0) +
                                                ($applicant['max_rate_private_relevant'] ?? 0) +
                                                ($applicant['max_rate_public_non_relevant'] ?? 0) +
                                                ($applicant['max_rate_public_relevant'] ?? 0) ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Trainings</td>
                                        <td><?= $applicant['rate_trainings'] ?? 0 ?></td>
                                        <td><?= $applicant['max_rate_trainings'] ?? 0 ?></td>
                                    </tr>
                                    <tr>
                                        <td>Skills and Competencies</td>
                                        <td><?= $applicant['rate_skills_competencies'] ?? 0 ?></td>
                                        <td><?= $applicant['max_rate_skills_competencies'] ?? 0 ?></td>
                                    </tr>
                                    <tr>
                                        <td>Knowledge</td>
                                        <td><?= $applicant['rate_knowledge'] ?? 0 ?></td>
                                        <td><?= $applicant['max_rate_knowledge'] ?? 0 ?></td>
                                    </tr>
                                    <tr>
                                        <td>Capability</td>
                                        <td><?= $applicant['rate_capability'] ?? 0 ?></td>
                                        <td><?= $applicant['max_rate_capability'] ?? 0 ?></td>
                                    </tr>
                                    <tr>
                                        <td>Public Service Status</td>
                                        <td><?= $applicant['rate_public_service'] ?? 0 ?></td>
                                        <td><?= $applicant['max_rate_public_service'] ?? 0 ?></td>
                                    </tr>
                                    <tr class="text-bold bg-secondary bg-opacity-10">
                                        <td>Total</td>
                                        <td><?=
                                            ($applicant['rate_age'] ?? 0) +
                                                ($applicant['rate_qualification'] ?? 0) +
                                                ($applicant['rate_trainings'] ?? 0) +
                                                ($applicant['rate_skills_competencies'] ?? 0) +
                                                ($applicant['rate_knowledge'] ?? 0) +
                                                ($applicant['rate_public_service'] ?? 0) +
                                                ($applicant['rate_private_non_relevant'] ?? 0) +
                                                ($applicant['rate_private_relevant'] ?? 0) +
                                                ($applicant['rate_public_non_relevant'] ?? 0) +
                                                ($applicant['rate_public_relevant'] ?? 0) +
                                                ($applicant['rate_capability'] ?? 0)
                                            ?></td>
                                        <td><?=
                                            ($applicant['max_rate_age'] ?? 0) +
                                                ($applicant['max_rate_qualification'] ?? 0) +
                                                ($applicant['max_rate_trainings'] ?? 0) +
                                                ($applicant['max_rate_skills_competencies'] ?? 0) +
                                                ($applicant['max_rate_knowledge'] ?? 0) +
                                                ($applicant['max_rate_public_service'] ?? 0) +
                                                ($applicant['max_rate_private_non_relevant'] ?? 0) +
                                                ($applicant['max_rate_private_relevant'] ?? 0) +
                                                ($applicant['max_rate_public_non_relevant'] ?? 0) +
                                                ($applicant['max_rate_public_relevant'] ?? 0) +
                                                ($applicant['max_rate_capability'] ?? 0)
                                            ?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>

<!-- Remove markdown-related scripts -->
<style>
    .chat-message {
        margin: 10px 0;
        padding: 15px;
        border-radius: 5px;
        max-width: 100%;
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
        font-family: Arial, sans-serif;
    }

    .user-message {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
    }

    .bot-message {
        background-color: #e9ecef;
        border: 1px solid #dee2e6;
        color: #212529;
    }

    .message-info {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
        padding-top: 5px;
        border-top: 1px solid #dee2e6;
    }

    .loading-indicator {
        display: none;
        text-align: center;
        padding: 10px;
        color: #6c757d;
    }

    .loading-indicator i {
        margin-right: 5px;
    }
</style>

<script>
    $(document).ready(function() {
        const chatContainer = $('#ai-chat-container');
        const analyzeButton = $('#analyzeButton');
        const remarksTextarea = $('#remarks');

        function addMessage(content, sender, processingTime = null, model = null) {
            const messageDiv = $('<div>')
                .addClass('chat-message')
                .addClass(`${sender}-message`);

            // Create a text node to prevent HTML interpretation
            const textNode = document.createTextNode(content);
            messageDiv.append(textNode);

            if (sender === 'bot') {
                // Update the remarks textarea with the AI's analysis
                remarksTextarea.val(content);

                // Add processing time and model info if available
                if (processingTime !== null || model !== null) {
                    const infoDiv = $('<div>').addClass('message-info');
                    let infoText = [];
                    if (model) infoText.push(`Model: ${model}`);
                    if (processingTime) infoText.push(`Response time: ${processingTime} seconds`);
                    const infoNode = document.createTextNode(infoText.join(' | '));
                    infoDiv.append(infoNode);
                    messageDiv.append(infoDiv);
                }
            }

            chatContainer.append(messageDiv);
            chatContainer.scrollTop(chatContainer[0].scrollHeight);
        }

        function showLoading() {
            const loadingDiv = $('<div>').addClass('loading-indicator').html(
                '<i class="fas fa-spinner fa-spin"></i> AI is analyzing...'
            );
            chatContainer.append(loadingDiv);
            loadingDiv.fadeIn();
            chatContainer.scrollTop(chatContainer[0].scrollHeight);
            return loadingDiv;
        }

        analyzeButton.on('click', function() {
            const loadingIndicator = showLoading();
            analyzeButton.prop('disabled', true);

            // Get the copied text from the AI Prompt button
            copyAllDetails();

            // Use a small delay to ensure the clipboard has been updated
            setTimeout(function() {
                navigator.clipboard.readText().then(function(clipText) {
                    // Add user message showing the analysis request
                    addMessage("Requesting AI analysis for applicant...", "user");

                    // Make AJAX call to your backend
                    $.ajax({
                        url: '<?= base_url('ai/analyze') ?>',
                        method: 'POST',
                        data: {
                            prompt: clipText
                        },
                        success: function(response) {
                            loadingIndicator.remove();
                            if (response.status === 'success') {
                                addMessage(response.message, 'bot', response.processingTime, response.model);
                            } else {
                                addMessage('Error: ' + response.message, 'bot', response.processingTime, response.model);
                            }
                        },
                        error: function() {
                            loadingIndicator.remove();
                            addMessage('Sorry, there was an error processing your request.', 'bot');
                        },
                        complete: function() {
                            analyzeButton.prop('disabled', false);
                        }
                    });
                }).catch(function(err) {
                    loadingIndicator.remove();
                    addMessage('Error: Unable to access clipboard. Please try again.', 'bot');
                    analyzeButton.prop('disabled', false);
                });
            }, 100);
        });
    });
</script>
<?= $this->endSection() ?>