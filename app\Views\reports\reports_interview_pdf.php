<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Report - <?= esc($position['designation']) ?></title>
    <style>
        @page {
            size: A4 landscape;
            margin: 1cm;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        
        .header h3 {
            margin: 5px 0;
            font-size: 12px;
            color: #888;
        }
        
        .table-container {
            width: 100%;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #333;
            padding: 6px 4px;
            text-align: center;
            vertical-align: middle;
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 9px;
        }
        
        .rank-col {
            width: 5%;
        }
        
        .name-col {
            width: 20%;
            text-align: left;
        }
        
        .interviewer-col {
            width: <?= count($interviewers) > 0 ? floor(50 / count($interviewers)) : 10 ?>%;
        }
        
        .total-col {
            width: 8%;
        }
        
        .percentage-col {
            width: 8%;
        }
        
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 5px;
            background-color: white;
        }
        
        .rank-number {
            font-weight: bold;
            font-size: 11px;
        }
        
        .applicant-name {
            font-weight: bold;
        }
        
        .total-score {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        
        .print-date {
            font-size: 9px;
            color: #999;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><?= esc($organization['name']) ?></h1>
        <h2><?= esc($positionGroup['group_name']) ?></h2>
        <h3><?= esc($position['designation']) ?> - Interview Results Report</h3>
        <div class="print-date">Generated on: <?= date('F d, Y \a\t g:i A') ?></div>
    </div>

    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th class="rank-col">Rank</th>
                    <th class="name-col">Interviewee Name</th>
                    <?php foreach ($interviewers as $interviewer): ?>
                        <th class="interviewer-col">
                            <?= esc($interviewer['interviewer_name']) ?><br>
                            <small><?= esc($interviewer['interviewer_position']) ?></small>
                        </th>
                    <?php endforeach; ?>
                    <th class="total-col">Total Score</th>
                    <th class="total-col">Total Out Of</th>
                    <th class="percentage-col">Percentage</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($applicants)): ?>
                    <tr>
                        <td colspan="<?= 5 + count($interviewers) ?>" style="text-align: center; padding: 20px;">
                            No interviewees found for this position.
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($applicants as $applicant): ?>
                        <tr>
                            <td class="rank-number"><?= $applicant['rank'] ?></td>
                            <td class="applicant-name" style="text-align: left;">
                                <?= esc($applicant['name']) ?>
                            </td>
                            <?php foreach ($interviewers as $interviewer): ?>
                                <td>
                                    <?php if (isset($applicant['interviewer_totals'][$interviewer['id']])): ?>
                                        <?= number_format($applicant['interviewer_totals'][$interviewer['id']]['total'], 1) ?>
                                    <?php else: ?>
                                        N/A
                                    <?php endif; ?>
                                </td>
                            <?php endforeach; ?>
                            <td class="total-score">
                                <?= number_format($applicant['grand_total'], 1) ?>
                            </td>
                            <td>
                                <?= number_format($applicant['max_possible_total'], 1) ?>
                            </td>
                            <td>
                                <?= number_format($applicant['percentage'], 1) ?>%
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <div class="footer">
        <p><strong>Selmasta Five</strong> - Interview Management System</p>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
            
            // Close window after printing (optional)
            window.onafterprint = function() {
                window.close();
            };
        };
    </script>
</body>
</html>
