<?php

namespace App\Controllers;

use App\Models\PositionsGroupModel;
use App\Models\PositionModel;
use App\Models\ApplicantModel;
use App\Models\InterviewQuestionModel;
use App\Models\InterviewerModel;
use App\Models\InterviewDataModel;

class Interview extends BaseController
{
    protected $positionsGroupModel;
    protected $positionModel;
    protected $applicantModel;
    protected $interviewQuestionModel;
    protected $interviewerModel;
    protected $interviewDataModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->positionsGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionModel();
        $this->applicantModel = new ApplicantModel();
        $this->interviewQuestionModel = new InterviewQuestionModel();
        $this->interviewerModel = new InterviewerModel();
        $this->interviewDataModel = new InterviewDataModel();
    }

    /**
     * Display list of positions due for interview
     * GET /interviews
     */
    public function index()
    {
        $data['title'] = 'Interviews';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position groups ordered by priority
        $positionGroups = $this->positionsGroupModel->where('org_id', $orgId)
            ->orderBy('priority', 'ASC')
            ->orderBy('group_name', 'ASC')
            ->findAll();

        $interviewData = [];

        foreach ($positionGroups as $group) {
            // Get positions marked for interview in this group
            $positions = $this->positionModel->where('position_group_id', $group['id'])
                ->where('org_id', $orgId)
                ->where('for_interview', 1)
                ->where('is_active', 1)
                ->orderBy('position_no', 'ASC')
                ->findAll();

            if (!empty($positions)) {
                $groupData = [
                    'id' => $group['id'],
                    'group_name' => $group['group_name'],
                    'priority' => $group['priority'],
                    'description' => $group['description'],
                    'positions' => []
                ];

                foreach ($positions as $position) {
                    // Count shortlisted applicants for this position
                    $shortlistedCount = $this->applicantModel->where('position_id', $position['id'])
                        ->where('org_id', $orgId)
                        ->where('application_status', 'Shortlisted')
                        ->countAllResults();

                    if ($shortlistedCount > 0) {
                        $positionData = [
                            'id' => $position['id'],
                            'position_no' => $position['position_no'],
                            'designation' => $position['designation'],
                            'classification' => $position['classification'],
                            'award' => $position['award'],
                            'shortlisted_count' => $shortlistedCount,
                            'created_at' => $position['created_at'],
                            'updated_at' => $position['updated_at']
                        ];

                        $groupData['positions'][] = $positionData;
                    }
                }

                // Only add group if it has positions with shortlisted applicants
                if (!empty($groupData['positions'])) {
                    $interviewData[] = $groupData;
                }
            }
        }

        $data['interviewData'] = $interviewData;

        return view('interviews/interview_index', $data);
    }

    /**
     * Open interview for a specific position
     * GET /interviews/open/{position_id}
     */
    public function open($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Open Interview';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->where('for_interview', 1)
            ->where('is_active', 1)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found or not available for interview.');
        }

        // Get position group details
        $positionGroup = $this->positionsGroupModel->find($position['position_group_id']);

        // Get shortlisted applicants for this position
        $applicants = $this->applicantModel->where('position_id', $positionId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->orderBy('name', 'ASC')
            ->findAll();

        // Get count of interview questions for this position
        $questionsCount = $this->interviewQuestionModel->where('position_id', $positionId)
            ->countAllResults();

        // Get count of interviewers for this position
        $interviewersCount = $this->interviewerModel->where('position_id', $positionId)
            ->countAllResults();

        // Calculate interview progress for each applicant
        $applicantProgress = [];
        if ($questionsCount > 0 && $interviewersCount > 0) {
            $totalExpectedScores = $questionsCount * $interviewersCount;

            foreach ($applicants as $applicant) {
                // Count how many scores have been entered for this applicant
                $enteredScores = $this->interviewDataModel->where('applicant_id', $applicant['id'])
                    ->where('position_id', $positionId)
                    ->where('is_deleted', false)
                    ->countAllResults();

                $progressPercentage = ($enteredScores / $totalExpectedScores) * 100;

                $applicantProgress[$applicant['id']] = [
                    'entered_scores' => $enteredScores,
                    'total_expected' => $totalExpectedScores,
                    'percentage' => $progressPercentage,
                    'is_complete' => $enteredScores >= $totalExpectedScores
                ];
            }
        }

        $data['position'] = $position;
        $data['positionGroup'] = $positionGroup;
        $data['applicants'] = $applicants;
        $data['questionsCount'] = $questionsCount;
        $data['interviewersCount'] = $interviewersCount;
        $data['applicantProgress'] = $applicantProgress;

        return view('interviews/interview_open', $data);
    }

    // ==================== INTERVIEW QUESTIONS CRUD ====================

    /**
     * Display interview questions for a position
     * GET /interviews/questions/{position_id}
     */
    public function questions($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Interview Questions';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get questions for this position
        $questions = $this->interviewQuestionModel->getQuestionsByPosition($positionId);

        $data['position'] = $position;
        $data['questions'] = $questions;
        $data['positionId'] = $positionId;

        return view('interviews/interview_questions', $data);
    }

    /**
     * Show form to create new interview question
     * GET /interviews/questions/create/{position_id}
     */
    public function createQuestion($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Create Interview Question';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data['position'] = $position;
        $data['positionId'] = $positionId;

        return view('interviews/interview_question_create', $data);
    }

    /**
     * Store new interview question
     * POST /interviews/questions/store/{position_id}
     */
    public function storeQuestion($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Validate position exists
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get next question number
        $lastQuestion = $this->interviewQuestionModel->where('position_id', $positionId)
            ->orderBy('question_no', 'DESC')
            ->first();
        $nextQuestionNo = ($lastQuestion['question_no'] ?? 0) + 1;

        $data = [
            'position_id' => $positionId,
            'question_no' => $nextQuestionNo,
            'question_text' => $this->request->getPost('question_text'),
            'set_score' => $this->request->getPost('set_score') ?: 0,
            'created_by' => session('user_id')
        ];

        if ($this->interviewQuestionModel->insert($data)) {
            return redirect()->to('interviews/questions/' . $positionId)
                ->with('success', 'Interview question created successfully.');
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create interview question.');
        }
    }

    /**
     * Show form to edit interview question
     * GET /interviews/questions/edit/{question_id}
     */
    public function editQuestion($questionId = null)
    {
        if (!$questionId) {
            return redirect()->to('interviews')->with('error', 'Question ID is required.');
        }

        $data['title'] = 'Edit Interview Question';
        $data['menu'] = 'interviews';

        // Get question details
        $question = $this->interviewQuestionModel->find($questionId);

        if (!$question) {
            return redirect()->to('interviews')->with('error', 'Question not found.');
        }

        // Get position details
        $position = $this->positionModel->find($question['position_id']);

        $data['question'] = $question;
        $data['position'] = $position;

        return view('interviews/interview_question_edit', $data);
    }

    /**
     * Update interview question
     * POST /interviews/questions/update/{question_id}
     */
    public function updateQuestion($questionId = null)
    {
        if (!$questionId) {
            return redirect()->to('interviews')->with('error', 'Question ID is required.');
        }

        // Get question details
        $question = $this->interviewQuestionModel->find($questionId);

        if (!$question) {
            return redirect()->to('interviews')->with('error', 'Question not found.');
        }

        $data = [
            'question_text' => $this->request->getPost('question_text'),
            'set_score' => $this->request->getPost('set_score') ?: 0,
            'updated_by' => session('user_id')
        ];

        if ($this->interviewQuestionModel->update($questionId, $data)) {
            return redirect()->to('interviews/questions/' . $question['position_id'])
                ->with('success', 'Interview question updated successfully.');
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update interview question.');
        }
    }

    /**
     * Delete interview question
     * POST /interviews/questions/delete/{question_id}
     */
    public function deleteQuestion($questionId = null)
    {
        if (!$questionId) {
            return redirect()->to('interviews')->with('error', 'Question ID is required.');
        }

        // Get question details
        $question = $this->interviewQuestionModel->find($questionId);

        if (!$question) {
            return redirect()->to('interviews')->with('error', 'Question not found.');
        }

        if ($this->interviewQuestionModel->delete($questionId)) {
            return redirect()->to('interviews/questions/' . $question['position_id'])
                ->with('success', 'Interview question deleted successfully.');
        } else {
            return redirect()->back()
                ->with('error', 'Failed to delete interview question.');
        }
    }

    // ==================== INTERVIEWERS CRUD ====================

    /**
     * Display interviewers for a position
     * GET /interviews/interviewers/{position_id}
     */
    public function interviewers($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Interviewers';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->getInterviewersByPosition($positionId);

        $data['position'] = $position;
        $data['interviewers'] = $interviewers;
        $data['positionId'] = $positionId;

        return view('interviews/interview_interviewers', $data);
    }

    /**
     * Show form to create new interviewer
     * GET /interviews/interviewers/create/{position_id}
     */
    public function createInterviewer($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Add Interviewer';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data['position'] = $position;
        $data['positionId'] = $positionId;

        return view('interviews/interview_interviewer_create', $data);
    }

    /**
     * Store new interviewer
     * POST /interviews/interviewers/store/{position_id}
     */
    public function storeInterviewer($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Validate position exists
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data = [
            'position_id' => $positionId,
            'interviewer_name' => $this->request->getPost('interviewer_name'),
            'interviewer_position' => $this->request->getPost('interviewer_position'),
            'created_by' => session('user_id')
        ];

        if ($this->interviewerModel->insert($data)) {
            return redirect()->to('interviews/interviewers/' . $positionId)
                ->with('success', 'Interviewer added successfully.');
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to add interviewer.');
        }
    }

    /**
     * Show form to edit interviewer
     * GET /interviews/interviewers/edit/{interviewer_id}
     */
    public function editInterviewer($interviewerId = null)
    {
        if (!$interviewerId) {
            return redirect()->to('interviews')->with('error', 'Interviewer ID is required.');
        }

        $data['title'] = 'Edit Interviewer';
        $data['menu'] = 'interviews';

        // Get interviewer details
        $interviewer = $this->interviewerModel->find($interviewerId);

        if (!$interviewer) {
            return redirect()->to('interviews')->with('error', 'Interviewer not found.');
        }

        // Get position details
        $position = $this->positionModel->find($interviewer['position_id']);

        $data['interviewer'] = $interviewer;
        $data['position'] = $position;

        return view('interviews/interview_interviewer_edit', $data);
    }

    /**
     * Update interviewer
     * POST /interviews/interviewers/update/{interviewer_id}
     */
    public function updateInterviewer($interviewerId = null)
    {
        if (!$interviewerId) {
            return redirect()->to('interviews')->with('error', 'Interviewer ID is required.');
        }

        // Get interviewer details
        $interviewer = $this->interviewerModel->find($interviewerId);

        if (!$interviewer) {
            return redirect()->to('interviews')->with('error', 'Interviewer not found.');
        }

        $data = [
            'interviewer_name' => $this->request->getPost('interviewer_name'),
            'interviewer_position' => $this->request->getPost('interviewer_position'),
            'updated_by' => session('user_id')
        ];

        if ($this->interviewerModel->update($interviewerId, $data)) {
            return redirect()->to('interviews/interviewers/' . $interviewer['position_id'])
                ->with('success', 'Interviewer updated successfully.');
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update interviewer.');
        }
    }

    /**
     * Delete interviewer
     * POST /interviews/interviewers/delete/{interviewer_id}
     */
    public function deleteInterviewer($interviewerId = null)
    {
        if (!$interviewerId) {
            return redirect()->to('interviews')->with('error', 'Interviewer ID is required.');
        }

        // Get interviewer details
        $interviewer = $this->interviewerModel->find($interviewerId);

        if (!$interviewer) {
            return redirect()->to('interviews')->with('error', 'Interviewer not found.');
        }

        if ($this->interviewerModel->delete($interviewerId)) {
            return redirect()->to('interviews/interviewers/' . $interviewer['position_id'])
                ->with('success', 'Interviewer deleted successfully.');
        } else {
            return redirect()->back()
                ->with('error', 'Failed to delete interviewer.');
        }
    }

    // ==================== INTERVIEW DATA CRUD ====================

    /**
     * View interview data for an applicant
     * GET /interviews/data/view/{applicant_id}
     */
    public function viewInterviewData($applicantId = null)
    {
        if (!$applicantId) {
            return redirect()->to('interviews')->with('error', 'Applicant ID is required.');
        }

        $orgId = session('org_id');

        // Get applicant details
        $applicant = $this->applicantModel->where('id', $applicantId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->first();

        if (!$applicant) {
            return redirect()->to('interviews')->with('error', 'Applicant not found or not shortlisted.');
        }

        // Get position details
        $position = $this->positionModel->find($applicant['position_id']);

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get interview questions for this position
        $questions = $this->interviewQuestionModel->getQuestionsByPosition($applicant['position_id']);

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->getInterviewersByPosition($applicant['position_id']);

        // Get existing interview data for this applicant
        $existingData = $this->interviewDataModel->where('applicant_id', $applicantId)
            ->where('position_id', $applicant['position_id'])
            ->where('is_deleted', false)
            ->findAll();

        // Organize existing data by interviewer and question
        $scores = [];
        foreach ($existingData as $data) {
            $scores[$data['interviewer_id']][$data['question_id']] = [
                'score' => $data['score'],
                'comments' => $data['comments']
            ];
        }

        $data['title'] = 'Interview Data - ' . $applicant['name'];
        $data['menu'] = 'interviews';
        $data['applicant'] = $applicant;
        $data['position'] = $position;
        $data['questions'] = $questions;
        $data['interviewers'] = $interviewers;
        $data['scores'] = $scores;

        return view('interviews/interview_data_view', $data);
    }

    /**
     * Store interview data scores
     * POST /interviews/data/store/{applicant_id}
     */
    public function storeInterviewData($applicantId = null)
    {
        if (!$applicantId) {
            return redirect()->to('interviews')->with('error', 'Applicant ID is required.');
        }

        $orgId = session('org_id');

        // Get applicant details
        $applicant = $this->applicantModel->where('id', $applicantId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->first();

        if (!$applicant) {
            return redirect()->to('interviews')->with('error', 'Applicant not found or not shortlisted.');
        }

        $scores = $this->request->getPost('scores');
        $comments = $this->request->getPost('comments');

        if (empty($scores)) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'No scores provided.');
        }

        $successCount = 0;
        $errorCount = 0;

        foreach ($scores as $interviewerId => $questionScores) {
            foreach ($questionScores as $questionId => $score) {
                // Check if data already exists
                $existingData = $this->interviewDataModel->where([
                    'applicant_id' => $applicantId,
                    'position_id' => $applicant['position_id'],
                    'interviewer_id' => $interviewerId,
                    'question_id' => $questionId,
                    'is_deleted' => false
                ])->first();

                // Skip if score is empty or null
                if ($score === '' || $score === null) {
                    // If there's existing data and score is now empty, delete it
                    if ($existingData) {
                        if ($this->interviewDataModel->delete($existingData['id'])) {
                            $successCount++;
                        } else {
                            $errorCount++;
                        }
                    }
                    continue;
                }

                $dataToSave = [
                    'position_id' => $applicant['position_id'],
                    'interviewer_id' => $interviewerId,
                    'applicant_id' => $applicantId,
                    'question_id' => $questionId,
                    'score' => floatval($score),
                    'comments' => isset($comments[$interviewerId][$questionId]) ? $comments[$interviewerId][$questionId] : '',
                    'created_by' => session('user_id'),
                    'updated_by' => session('user_id')
                ];

                if ($existingData) {
                    // Update existing data
                    unset($dataToSave['created_by']);
                    if ($this->interviewDataModel->update($existingData['id'], $dataToSave)) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                } else {
                    // Insert new data
                    if ($this->interviewDataModel->insert($dataToSave)) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                }
            }
        }

        if ($successCount > 0 && $errorCount == 0) {
            return redirect()->to('interviews/data/view/' . $applicantId)
                ->with('success', 'Interview scores saved successfully.');
        } elseif ($successCount > 0 && $errorCount > 0) {
            return redirect()->to('interviews/data/view/' . $applicantId)
                ->with('warning', "Saved {$successCount} scores, but {$errorCount} failed to save.");
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to save interview scores.');
        }
    }
}
