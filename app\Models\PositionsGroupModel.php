<?php

namespace App\Models;

use CodeIgniter\Model;

class PositionsGroupModel extends Model
{
    protected $table      = 'positions_groups';        // Name of the table
    protected $primaryKey = 'id';                      // Primary key of the table

    protected $useAutoIncrement = true;                // Primary key is auto-incremented

    // Fields that are allowed to be inserted/updated
    protected $allowedFields = [
        'org_id',
        'priority',
        'group_name',
        'description',
        'created_by',
        'updated_by'
        
    ];

    // Automatically handle the timestamps for created_at and updated_at
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Optional: Soft deletes can be enabled if needed
    protected $useSoftDeletes = false;

    // Optional: Define default values (you can add any defaults if required)
    protected $defaultValues = [
        // Define default values if necessary
    ];
}
