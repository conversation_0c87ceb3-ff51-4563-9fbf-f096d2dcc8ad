<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Reports Dashboard</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Reports</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Summary Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3><?= $totalApplicants ?></h3>
                            <p>Total Applicants</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3><?= $totalPositions ?></h3>
                            <p>Total Positions</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3><?= count($positionGroups) ?></h3>
                            <p>Position Groups</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3><?= number_format($ratingProgress, 1) ?>%</h3>
                            <p>Rating Progress</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top 3 Highest Rated Applicants -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Top 3 Highest Rated Applicants</h3>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Name</th>
                                        <th>Position</th>
                                        <th>Total Rating</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($topApplicants as $index => $applicant): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td><?= esc($applicant['name']) ?></td>
                                        <td><?= esc($applicant['position_title']) ?></td>
                                        <td><?= number_format($applicant['total_rating'], 2) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Applicants per Position Group</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="applicantsPerGroupChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Rating Distribution</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="ratingDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Applicants per Position Group Chart
                var applicantsPerGroupCtx = document.getElementById('applicantsPerGroupChart').getContext('2d');
                new Chart(applicantsPerGroupCtx, {
                    type: 'bar',
                    data: {
                        labels: <?= json_encode(array_column($positionGroups, 'group_name')) ?>,
                        datasets: [{
                            label: 'Number of Applicants',
                            data: <?= json_encode(array_column($positionGroups, 'total_applicants')) ?>,
                            backgroundColor: 'rgba(60,141,188,0.8)',
                            borderColor: 'rgba(60,141,188,1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Rating Distribution Chart
                var ratingDistributionCtx = document.getElementById('ratingDistributionChart').getContext('2d');
                new Chart(ratingDistributionCtx, {
                    type: 'pie',
                    data: {
                        labels: ['Excellent (80-100)', 'Good (60-79)', 'Average (40-59)', 'Below Average (20-39)', 'Poor (0-19)'],
                        datasets: [{
                            data: <?= json_encode($ratingDistribution) ?>,
                            backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#fd7e14', '#dc3545']
                        }]
                    },
                    options: {
                        responsive: true
                    }
                });
            });
            </script>

            <!-- Position Groups Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Position Groups</h3>
                            <div class="card-tools">
                                <a href="<?= base_url('reports/preSelection') ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-file-alt"></i> View Pre-Selection (RS.3.7A)
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Group Name</th>
                                        <th>Total Positions</th>
                                        <th>Total Applicants</th>
                                        <th>Rating Progress</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positionGroups as $group): ?>
                                    <tr>
                                        <td><?= esc($group['group_name']) ?></td>
                                        <td><?= count($group['positions']) ?></td>
                                        <td><?= $group['total_applicants'] ?></td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar" style="width: <?= $group['rating_progress'] ?>%;" aria-valuenow="<?= $group['rating_progress'] ?>" aria-valuemin="0" aria-valuemax="100"><?= number_format($group['rating_progress'], 1) ?>%</div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="<?= base_url("reports/viewPositions/{$group['id']}") ?>" class="btn btn-primary btn-sm">View Report</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>



<?= $this->endSection() ?>
