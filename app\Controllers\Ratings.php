<?php

namespace App\Controllers;

use App\Models\PositionsGroupModel;
use App\Models\PositionModel;
use App\Models\ApplicantModel;

class Ratings extends BaseController
{
    protected $positionsGroupModel;
    protected $positionModel;
    protected $applicantModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->positionsGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionModel();
        $this->applicantModel = new ApplicantModel();
    }

    public function index()
    {
        $data['title'] = 'Ratings';
        $data['menu'] = 'ratings';
        $positionGroups = $this->positionsGroupModel->where('org_id', session('org_id'))
            ->orderBy('group_name', 'ASC')
            ->findAll();

        foreach ($positionGroups as &$group) {
            $group['positions'] = $this->positionModel->where('position_group_id', $group['id'])
                ->where('org_id', session('org_id'))
                ->orderBy('position_no', 'ASC')
                ->findAll();
            foreach ($group['positions'] as &$position) {
                $position['applicants'] = $this->applicantModel->where('position_id', $position['id'])
                    ->where('org_id', session('org_id'))
                    ->orderBy('name', 'ASC')
                    ->findAll();
            }
        }

        $data['positionGroups'] = $positionGroups;
        return view('ratings/ratings_index', $data);
    }

    public function viewPositions($groupId)
    {
        $data['title'] = 'View Positions';
        $data['menu'] = 'ratings';
        $positions = $this->positionModel->where('position_group_id', $groupId)
            ->where('org_id', session('org_id'))
            ->orderBy('position_no', 'ASC')
            ->findAll();

        foreach ($positions as &$position) {
            $applicants = $this->applicantModel->where('position_id', $position['id'])
                ->where('org_id', session('org_id'))
                ->orderBy('name', 'ASC')
                ->findAll();
            $position['applicants'] = $applicants;
        }

        $data['positions'] = $positions;
        $data['groupName'] = $this->positionsGroupModel->where('org_id', session('org_id'))->find($groupId)['group_name'];
        return view('ratings/ratings_view_positions', $data);
    }

    public function viewApplicants($positionId)
    {
        $data['title'] = 'View Applicants';
        $data['menu'] = 'ratings';
        $data['applicants'] = $this->applicantModel->where('position_id', $positionId)
            ->where('org_id', session('org_id'))
            ->orderBy('name', 'ASC')
            ->findAll();
        $data['position'] = $this->positionModel->where('org_id', session('org_id'))->find($positionId);
        return view('ratings/ratings_view_applicants', $data);
    }

    public function rateApplicant($applicantId)
    {
        $data['title'] = 'Rate Applicant';
        $data['menu'] = 'ratings';
        $applicant = $this->applicantModel->find($applicantId);
        $position = $this->positionModel->find($applicant['position_id']);

        $data['applicant'] = $applicant;
        $data['position'] = $position;
        return view('ratings/ratings_rate_applicant', $data);
    }

    public function updateApplicantRating($applicantId)
    {
        $applicant = $this->applicantModel->find($applicantId);
        $position = $this->positionModel->find($applicant['position_id']);
        $updateData = [
            'rate_age' => $this->request->getPost('rate_age'),
            'max_rate_age' => $this->request->getPost('max_rate_age'),
            'rate_qualification' => $this->request->getPost('rate_education'),
            'max_rate_qualification' => $this->request->getPost('max_rate_education'),
            'rate_capability' => $this->request->getPost('rate_capability'),
            'max_rate_capability' => $this->request->getPost('max_rate_capability'),
            'rate_experience' => 0, // We'll calculate this below
            'max_rate_experience' => 0, // We'll calculate this below
            'rate_trainings' => $this->request->getPost('rate_trainings'),
            'max_rate_trainings' => $this->request->getPost('max_rate_trainings'),
            'rate_skills_competencies' => $this->request->getPost('rate_skills_competencies'),
            'max_rate_skills_competencies' => $this->request->getPost('max_rate_skills_competencies'),
            'rate_knowledge' => $this->request->getPost('rate_knowledge'),
            'max_rate_knowledge' => $this->request->getPost('max_rate_knowledge'),
            'rate_public_service' => $this->request->getPost('rate_public_service'),
            'max_rate_public_service' => $this->request->getPost('max_rate_public_service'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session('username'),
        ];

        // Add all the max_rates excluding max_rate_experience
        $max_rates = [
            'max_rate_age',
            'max_rate_education',
            'max_rate_capability',
            'max_rate_trainings',
            'max_rate_skills_competencies',
            'max_rate_knowledge',
            'max_rate_public_service',
            'max_rate_private_non_relevant',
            'max_rate_private_relevant',
            'max_rate_public_non_relevant',
            'max_rate_public_relevant'
        ];

        $updateData['max_rate_total'] = 0;
        foreach ($max_rates as $rate) {
            $updateData['max_rate_total'] += $this->request->getPost($rate);
        }

        // Add all the rates excluding rate_experience
        $rates = [
            'rate_age',
            'rate_education',
            'rate_capability',
            'rate_trainings',
            'rate_skills_competencies',
            'rate_knowledge',
            'rate_public_service',
            'rate_private_non_relevant',
            'rate_private_relevant',
            'rate_public_non_relevant',
            'rate_public_relevant'
        ];

        $updateData['rate_total'] = 0;
        foreach ($rates as $rate) {
            $updateData['rate_total'] += $this->request->getPost($rate);
        }

        // Add rate_experience separately
        //$updateData['rate_total'] += $updateData['rate_experience'];

        // Calculate experience ratings
        $experienceTypes = [
            'private_non_relevant',
            'private_relevant',
            'public_non_relevant',
            'public_relevant'
        ];

        foreach ($experienceTypes as $type) {
            $rateKey = "rate_{$type}";
            $maxRateKey = "max_rate_{$type}";

            $updateData[$rateKey] = $this->request->getPost($rateKey);
            $updateData[$maxRateKey] = $this->request->getPost($maxRateKey);

            $updateData['rate_experience'] += $updateData[$rateKey];
            $updateData['max_rate_experience'] += $updateData[$maxRateKey];
        }

        if ($updateData['rate_age']  == 0) {
            $updateData['application_status'] = 'Eliminated';
            $updateData['app_status_reason'] = 'Passed Retirement Age';
        }

        // Filter out any fields that are not in the allowedFields
        $filteredUpdateData = array_intersect_key($updateData, array_flip($this->applicantModel->allowedFields));

        $this->applicantModel->update($applicantId, $filteredUpdateData);

        return redirect()->to(base_url("ratings/rateApplicant/{$applicantId}"))
            ->with('success', 'Applicant rated successfully');
    }

    /**
     * Rate ages for all applicants in the system
     * This method is called via AJAX from the ratings index page
     */
    public function rateAges()
    {
        // Verify if this is an AJAX request
        if (!$this->request->isAJAX()) {
            log_message('error', 'Non-AJAX request to rateAges');
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
        }

        try {
            // Validate CSRF token
            if (!$this->validateCSRF()) {
                log_message('error', 'CSRF validation failed in rateAges');
                return $this->response->setJSON(['success' => false, 'message' => 'Invalid security token']);
            }

            // Get all applicants with their ages
            $applicants = $this->applicantModel->where('org_id', session('org_id'))->findAll();

            if (empty($applicants)) {
                log_message('info', 'No applicants found to rate');
                return $this->response->setJSON(['success' => false, 'message' => 'No applicants found to rate']);
            }

            $updatedCount = 0;
            $eliminatedCount = 0;

            foreach ($applicants as $applicant) {
                if (!isset($applicant['age'])) {
                    log_message('warning', 'Applicant ID ' . $applicant['id'] . ' has no age field');
                    continue;
                }

                $age = intval($applicant['age']);
                $rateData = [
                    'max_rate_age' => 8, // Maximum possible age rating
                    'updated_by' => session('username')
                ];

                // Determine rate_age based on age ranges
                if ($age >= 65) {
                    $rateData['rate_age'] = 0;
                    $rateData['application_status'] = 'Eliminated';
                    $rateData['app_status_reason'] = 'Passed Retirement Age';
                    $eliminatedCount++;
                } elseif ($age >= 60) {
                    $rateData['rate_age'] = 1;
                } elseif ($age >= 54) {
                    $rateData['rate_age'] = 2;
                } elseif ($age >= 48) {
                    $rateData['rate_age'] = 3;
                } elseif ($age >= 42) {
                    $rateData['rate_age'] = 4;
                } elseif ($age >= 36) {
                    $rateData['rate_age'] = 5;
                } elseif ($age >= 30) {
                    $rateData['rate_age'] = 6;
                } elseif ($age >= 24) {
                    $rateData['rate_age'] = 7;
                } elseif ($age >= 18) {
                    $rateData['rate_age'] = 8;
                } else {
                    $rateData['rate_age'] = 1;
                    $eliminatedCount++;
                }

                try {
                    // Update the applicant record
                    $this->applicantModel->update($applicant['id'], $rateData);
                    $updatedCount++;
                } catch (\Exception $e) {
                    log_message('error', 'Failed to update applicant ID ' . $applicant['id'] . ': ' . $e->getMessage());
                }
            }

            log_message('info', "Successfully rated {$updatedCount} applicants. {$eliminatedCount} applicants were eliminated.");

            // Return success response
            return $this->response->setJSON([
                'success' => true,
                'message' => "Successfully rated {$updatedCount} applicants. {$eliminatedCount} applicants were eliminated due to age restrictions.",
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error in rateAges: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while rating ages: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Validate CSRF token
     */
    private function validateCSRF()
    {
        $csrf = csrf_hash();
        return $this->request->getPost(csrf_token()) === $csrf;
    }

    public function eliminate($id)
    {
        $applicant = $this->applicantModel->find($id);

        if (!$applicant) {
            return redirect()->back()->with('error', 'Applicant not found.');
        }

        $eliminationReason = $this->request->getPost('elimination_reason');

        // Update the applicant's status to 'Eliminated' and add the reason
        $this->applicantModel->update($id, [
            'application_status' => 'Eliminated',
            'app_status_reason' => $eliminationReason,
            'rate_qualification' => 1,
        ]);

        // Get the position ID to redirect back to the correct view
        $positionId = $applicant['position_id'];

        return redirect()->to(base_url("ratings/viewApplicants/{$positionId}"))
            ->with('success', 'Applicant has been eliminated.');
    }

    public function undoElimination($id)
    {
        $applicant = $this->applicantModel->find($id);

        if (!$applicant) {
            return redirect()->back()->with('error', 'Applicant not found.');
        }

        $undoEliminationReason = $this->request->getPost('undo_elimination_reason');

        // Update the applicant's status to 'Active' and add the reason for undoing elimination
        $this->applicantModel->update($id, [
            'application_status' => 'Active',
            'app_status_reason' => $undoEliminationReason,
            'rate_qualification' => 0,
        ]);

        // Get the position ID to redirect back to the correct view
        $positionId = $applicant['position_id'];

        return redirect()->to(base_url("ratings/viewApplicants/{$positionId}"))
            ->with('success', 'Applicant elimination has been undone.');
    }
}
