<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>

<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Positions</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <!-- Positions Card -->
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-briefcase mr-2"></i>
                                Positions for <?= $org['name'] ?>
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Positions Table Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h4 class="mb-0">
                                            <i class="fas fa-list mr-2"></i>
                                            Positions List
                                        </h4>
                                        <a href="<?= base_url("positions/create/{$org['id']}") ?>" class="btn btn-primary">
                                            <i class="fas fa-plus mr-1"></i>
                                            Add New Position
                                        </a>
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th width="5%">#</th>
                                                    <th width="10%">Position No</th>
                                                    <th width="15%">Designation</th>
                                                    <th width="10%">Classification</th>
                                                    <th width="10%">Award</th>
                                                    <th width="15%">Qualifications</th>
                                                    <th width="15%">Knowledge</th>
                                                    <th width="10%">Skills & Competencies</th>
                                                    <th width="10%">Job Experiences</th>
                                                    <th width="5%">Status</th>
                                                    <th width="5%">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $count = 1; ?>
                                                <?php foreach ($positions as $position): ?>
                                                    <tr>
                                                        <td class="align-middle"><?= $count++ ?></td>
                                                        <td class="align-middle"><?= esc($position['position_no']) ?></td>
                                                        <td class="align-middle"><?= esc($position['designation']) ?></td>
                                                        <td class="align-middle"><?= esc($position['classification']) ?></td>
                                                        <td class="align-middle"><?= esc($position['award']) ?></td>
                                                        <td class="align-middle"><?= esc($position['qualifications']) ?></td>
                                                        <td class="align-middle"><?= esc($position['knowledge']) ?></td>
                                                        <td class="align-middle"><?= esc($position['skills_competencies']) ?></td>
                                                        <td class="align-middle"><?= esc($position['job_experiences']) ?></td>
                                                        <td class="align-middle">
                                                            <span class="badge badge-<?= $position['is_active'] ? 'success' : 'danger' ?>">
                                                                <?= $position['is_active'] ? 'Active' : 'Inactive' ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="<?= base_url("positions/edit/{$position['id']}") ?>" 
                                                                   class="btn btn-warning btn-sm" 
                                                                   title="Edit Position">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="<?= base_url("positions/delete/{$position['id']}") ?>" 
                                                                   class="btn btn-danger btn-sm" 
                                                                   onclick="return confirm('Are you sure you want to delete this position no: <?= esc($position['position_no']) ?> ?')"
                                                                   title="Delete Position">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>