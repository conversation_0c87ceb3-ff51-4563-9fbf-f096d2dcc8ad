<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Line breaks and colors</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Line breaks and colors</h1>
Let's continue with an example which prints justified paragraphs. It also illustrates the use
of colors.
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'fpdf.php'</span><span class="kw">);

class </span>PDF <span class="kw">extends </span>FPDF
<span class="kw">{
function </span>Header<span class="kw">()
{
    global </span>$title<span class="kw">;

    </span><span class="cmt">// Arial bold 15
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">'B'</span><span class="kw">,</span>15<span class="kw">);
    </span><span class="cmt">// Calculate width of title and position
    </span>$w <span class="kw">= </span>$<span class="kw">this-&gt;</span>GetStringWidth<span class="kw">(</span>$title<span class="kw">)+</span>6<span class="kw">;
    </span>$<span class="kw">this-&gt;</span>SetX<span class="kw">((</span>210<span class="kw">-</span>$w<span class="kw">)/</span>2<span class="kw">);
    </span><span class="cmt">// Colors of frame, background and text
    </span>$<span class="kw">this-&gt;</span>SetDrawColor<span class="kw">(</span>0<span class="kw">,</span>80<span class="kw">,</span>180<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetFillColor<span class="kw">(</span>230<span class="kw">,</span>230<span class="kw">,</span>0<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetTextColor<span class="kw">(</span>220<span class="kw">,</span>50<span class="kw">,</span>50<span class="kw">);
    </span><span class="cmt">// Thickness of frame (1 mm)
    </span>$<span class="kw">this-&gt;</span>SetLineWidth<span class="kw">(</span>1<span class="kw">);
    </span><span class="cmt">// Title
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">,</span>9<span class="kw">,</span>$title<span class="kw">,</span>1<span class="kw">,</span>1<span class="kw">,</span><span class="str">'C'</span><span class="kw">,</span>true<span class="kw">);
    </span><span class="cmt">// Line break
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">(</span>10<span class="kw">);
}

function </span>Footer<span class="kw">()
{
    </span><span class="cmt">// Position at 1.5 cm from bottom
    </span>$<span class="kw">this-&gt;</span>SetY<span class="kw">(-</span>15<span class="kw">);
    </span><span class="cmt">// Arial italic 8
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">'I'</span><span class="kw">,</span>8<span class="kw">);
    </span><span class="cmt">// Text color in gray
    </span>$<span class="kw">this-&gt;</span>SetTextColor<span class="kw">(</span>128<span class="kw">);
    </span><span class="cmt">// Page number
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>0<span class="kw">,</span>10<span class="kw">,</span><span class="str">'Page '</span><span class="kw">.</span>$<span class="kw">this-&gt;</span>PageNo<span class="kw">(),</span>0<span class="kw">,</span>0<span class="kw">,</span><span class="str">'C'</span><span class="kw">);
}

function </span>ChapterTitle<span class="kw">(</span>$num<span class="kw">, </span>$label<span class="kw">)
{
    </span><span class="cmt">// Arial 12
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span>12<span class="kw">);
    </span><span class="cmt">// Background color
    </span>$<span class="kw">this-&gt;</span>SetFillColor<span class="kw">(</span>200<span class="kw">,</span>220<span class="kw">,</span>255<span class="kw">);
    </span><span class="cmt">// Title
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>0<span class="kw">,</span>6<span class="kw">,</span><span class="str">"Chapter </span>$num<span class="str"> : </span>$label<span class="str">"</span><span class="kw">,</span>0<span class="kw">,</span>1<span class="kw">,</span><span class="str">'L'</span><span class="kw">,</span>true<span class="kw">);
    </span><span class="cmt">// Line break
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">(</span>4<span class="kw">);
}

function </span>ChapterBody<span class="kw">(</span>$file<span class="kw">)
{
    </span><span class="cmt">// Read text file
    </span>$txt <span class="kw">= </span>file_get_contents<span class="kw">(</span>$file<span class="kw">);
    </span><span class="cmt">// Times 12
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Times'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span>12<span class="kw">);
    </span><span class="cmt">// Output justified text
    </span>$<span class="kw">this-&gt;</span>MultiCell<span class="kw">(</span>0<span class="kw">,</span>5<span class="kw">,</span>$txt<span class="kw">);
    </span><span class="cmt">// Line break
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    </span><span class="cmt">// Mention in italics
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">''</span><span class="kw">,</span><span class="str">'I'</span><span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>0<span class="kw">,</span>5<span class="kw">,</span><span class="str">'(end of excerpt)'</span><span class="kw">);
}

function </span>PrintChapter<span class="kw">(</span>$num<span class="kw">, </span>$title<span class="kw">, </span>$file<span class="kw">)
{
    </span>$<span class="kw">this-&gt;</span>AddPage<span class="kw">();
    </span>$<span class="kw">this-&gt;</span>ChapterTitle<span class="kw">(</span>$num<span class="kw">,</span>$title<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>ChapterBody<span class="kw">(</span>$file<span class="kw">);
}
}

</span>$pdf <span class="kw">= new </span>PDF<span class="kw">();
</span>$title <span class="kw">= </span><span class="str">'20000 Leagues Under the Seas'</span><span class="kw">;
</span>$pdf<span class="kw">-&gt;</span>SetTitle<span class="kw">(</span>$title<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>SetAuthor<span class="kw">(</span><span class="str">'Jules Verne'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>PrintChapter<span class="kw">(</span>1<span class="kw">,</span><span class="str">'A RUNAWAY REEF'</span><span class="kw">,</span><span class="str">'20k_c1.txt'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>PrintChapter<span class="kw">(</span>2<span class="kw">,</span><span class="str">'THE PROS AND CONS'</span><span class="kw">,</span><span class="str">'20k_c2.txt'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Output<span class="kw">();
</span>?&gt;</code></pre>
</div>
<p class='demo'><a href='tuto3.php' target='_blank' class='demo'>[Demo]</a></p>
The <a href='../doc/getstringwidth.htm'>GetStringWidth()</a> method allows to determine the length of a string in the current font,
which is used here to calculate the position and the width of the frame surrounding the title.
Then colors are set (via <a href='../doc/setdrawcolor.htm'>SetDrawColor()</a>, <a href='../doc/setfillcolor.htm'>SetFillColor()</a> and <a href='../doc/settextcolor.htm'>SetTextColor()</a>) and the
thickness of the line is set to 1 mm (instead of 0.2 by default) with <a href='../doc/setlinewidth.htm'>SetLineWidth()</a>. Finally,
we output the cell (the last parameter <code>true</code> indicates that the background must
be filled).
<br>
<br>
The method used to print the paragraphs is <a href='../doc/multicell.htm'>MultiCell()</a>. Each time a line reaches the
right extremity of the cell or a carriage return character is met, a line break is issued
and a new cell automatically created under the current one. Text is justified by default.
<br>
<br>
Two document properties are defined: the title (<a href='../doc/settitle.htm'>SetTitle()</a>) and the author (<a href='../doc/setauthor.htm'>SetAuthor()</a>).
There are several ways to view them in Adobe Reader. The first one is to open the file directly with
the reader, go to the File menu and choose the Properties option. The second one, also available from
the plug-in, is to right-click and select Document Properties. The third method is to type the Ctrl+D
key combination.
</body>
</html>
