<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Applicants by Rank - <?= esc($positionName) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("reports/viewPositions/{$groupId}") ?>"><?= esc($groupName) ?></a></li>
                        <li class="breadcrumb-item active">Applicants by Rank</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="text-center">
                                <img src="<?= imgcheck($org['org_logo']) ?>" alt="Organization Logo" class="img-fluid" style="align-items: center; max-width: 50px; display: block; margin: 0 auto;">
                            </div>
                            <h3 class="text-center"><?= session()->get('org_name') ?></h3>
                            <h4 class="text-center"><?= esc($groupName) ?></h4>
                            <h4 class="text-center"> <?= esc($positionNo) ?> - <?= esc($positionName) ?></h4>


                            <h3 class="card-title text-center">Ranked Applicants </h3>
                            <div class="float-right d-flex justify-content-between no-print">

                                <select id="noPrintDropdown" class="form-control mr-2" multiple>
                                    <!-- Options will be populated by JavaScript -->
                                </select>

                                <script>
                                    document.addEventListener("DOMContentLoaded", function() {
                                        const tableHeaders = document.querySelectorAll("thead th");
                                        const dropdown = document.getElementById("noPrintDropdown");
                                        const tableRows = document.querySelectorAll("tbody tr");

                                        // Populate the dropdown with table headers
                                        tableHeaders.forEach((header, index) => {
                                            const option = document.createElement("option");
                                            option.value = index;
                                            option.textContent = header.textContent;
                                            option.selected = header.classList.contains("no-print");
                                            dropdown.appendChild(option);
                                        });

                                        // Function to toggle "no-print" class on entire column
                                        function toggleColumnPrint(columnIndex, shouldHide) {
                                            // Toggle class on the header
                                            if (shouldHide) {
                                                tableHeaders[columnIndex].classList.add("no-print");
                                            } else {
                                                tableHeaders[columnIndex].classList.remove("no-print");
                                            }

                                            // Toggle class on each cell in the column
                                            tableRows.forEach(row => {
                                                const cell = row.children[columnIndex];
                                                if (cell) {
                                                    if (shouldHide) {
                                                        cell.classList.add("no-print");
                                                    } else {
                                                        cell.classList.remove("no-print");
                                                    }
                                                }
                                            });
                                        }

                                        // Listen for changes in the dropdown to toggle the class on entire columns
                                        dropdown.addEventListener("change", function() {
                                            Array.from(dropdown.options).forEach((option, index) => {
                                                toggleColumnPrint(index, option.selected);
                                            });
                                        });
                                    });
                                </script>

                                <style>
                                    @media print {
                                        .no-print {
                                            display: none;
                                        }
                                    }
                                </style>




                                <select id="sortDropdown" class="form-control mr-2">
                                    <option value="">Sort by - All</option>
                                    <?php for ($i = 1; $i <= 10; $i++): ?>
                                        <option value="<?= $i ?>"><?= $i ?></option>
                                    <?php endfor; ?>
                                </select>
                                <select id="statusDropdown" class="form-control">
                                    <option value="All">Status - All</option>
                                    <option value="Shortlisted">Shortlisted</option>
                                    <option value="Eliminated">Eliminated</option>
                                    <option value="Withdrawn">Withdrawn</option>
                                </select>
                            </div>

                            <script>
                                document.addEventListener("DOMContentLoaded", function() {
                                    const sortDropdown = document.getElementById("sortDropdown");
                                    const statusDropdown = document.getElementById("statusDropdown");
                                    const tableBody = document.querySelector("table tbody");
                                    const allRows = Array.from(tableBody.rows); // Store all table rows initially

                                    // Function to display the selected number of rows and filter by status
                                    function displayRows(count, status) {
                                        // First, hide all rows
                                        allRows.forEach(row => (row.style.display = "none"));

                                        // Then, show the selected number of rows that match the status
                                        let shownRows = 0;
                                        for (let i = 0; i < allRows.length && shownRows < count; i++) {
                                            const rowStatus = allRows[i].cells[5].textContent.trim(); // Assuming status is in the 6th column
                                            if (status === "All" || rowStatus === status) {
                                                allRows[i].style.display = "";
                                                shownRows++;
                                            }
                                        }
                                    }

                                    // Add event listener to the sort dropdown
                                    sortDropdown.addEventListener("change", function() {
                                        const selectedValue = parseInt(this.value, 10) || allRows.length;
                                        const selectedStatus = statusDropdown.value;
                                        displayRows(selectedValue, selectedStatus);
                                    });

                                    // Add event listener to the status dropdown
                                    statusDropdown.addEventListener("change", function() {
                                        const selectedValue = parseInt(sortDropdown.value, 10) || allRows.length;
                                        const selectedStatus = this.value;
                                        displayRows(selectedValue, selectedStatus);
                                    });

                                    // Initially display all rows
                                    displayRows(allRows.length, "All");
                                });
                            </script>

                        </div>
                        <div class="card-body">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Name</th>
                                        <th>Position No.</th>
                                        <th>Position</th>
                                        <th>Total Rating</th>
                                        <th>Status</th>
                                        <th>Reason</th>
                                        <th class="no-print">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($rankedApplicants as $applicant): ?>
                                        <tr>
                                            <td><?= esc($applicant['overall_rank']) ?></td>
                                            <td><?= esc($applicant['name']) ?></td>
                                            <td><?= esc($applicant['position_no']) ?></td>
                                            <td><?= esc($applicant['position']) ?></td>
                                            <td><?= esc($applicant['rate_total']) ?> / 61</td>
                                            <td><?= esc($applicant['application_status']) ?></td>
                                            <td><?= esc($applicant['app_status_reason']) ?></td>
                                            <td class="no-print">


                                                <!-- Shortlist modal form trigger -->
                                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#shortlistModal<?= $applicant['id'] ?>">Shortlist</button>

                                                <!-- Shortlist modal -->
                                                <div class="modal fade" id="shortlistModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="shortlistModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="shortlistModalLabel<?= $applicant['id'] ?>">Shortlist Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <form action="<?= base_url("applicants/shortlist/{$applicant['id']}") ?>" method="post">
                                                                    <div class="form-group">
                                                                        <label for="shortlistReason<?= $applicant['id'] ?>">Reason for Shortlisting</label>
                                                                        <textarea class="form-control" id="shortlistReason<?= $applicant['id'] ?>" name="shortlistReason" required></textarea>
                                                                    </div>
                                                                    <button type="submit" class="btn btn-primary">Shortlist</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Eliminate modal form trigger -->
                                                <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#eliminateModal<?= $applicant['id'] ?>">Eliminate</button>

                                                <!-- Eliminate modal -->
                                                <div class="modal fade" id="eliminateModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="eliminateModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="eliminateModalLabel<?= $applicant['id'] ?>">Eliminate Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <form action="<?= base_url("applicants/eliminate/{$applicant['id']}") ?>" method="post">
                                                                    <div class="form-group">
                                                                        <label for="eliminateReason<?= $applicant['id'] ?>">Reason for Elimination</label>
                                                                        <textarea class="form-control" id="eliminateReason<?= $applicant['id'] ?>" name="eliminateReason" required></textarea>
                                                                    </div>
                                                                    <button type="submit" class="btn btn-danger">Eliminate</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Withdraw modal form trigger -->
                                                <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#withdrawModal<?= $applicant['id'] ?>">Withdraw</button>

                                                <!-- Withdraw modal -->
                                                <div class="modal fade" id="withdrawModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="withdrawModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="withdrawModalLabel<?= $applicant['id'] ?>">Withdraw Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <form action="<?= base_url("applicants/withdraw/{$applicant['id']}") ?>" method="post">
                                                                    <div class="form-group">
                                                                        <label for="withdrawReason<?= $applicant['id'] ?>">Reason for Withdrawal</label>
                                                                        <textarea class="form-control" id="withdrawReason<?= $applicant['id'] ?>" name="withdrawReason" required></textarea>
                                                                    </div>
                                                                    <button type="submit" class="btn btn-warning">Withdraw</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>