<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('positions') ?>">Position Groups</a></li>
                        <li class="breadcrumb-item active">View Group</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <!-- Group Details Card -->
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-layer-group mr-2"></i>
                                <?= esc($group['group_name']) ?>
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h5><i class="fas fa-info-circle mr-2"></i>Description:</h5>
                                    <p class="text-muted"><?= esc($group['description']) ?></p>
                                </div>
                            </div>

                            <!-- Positions Table Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h4 class="mb-0">
                                            <i class="fas fa-list mr-2"></i>
                                            Positions List
                                        </h4>
                                        <a href="<?= site_url('positions/' . $group['id'] . '/new') ?>" class="btn btn-primary">
                                            <i class="fas fa-plus mr-1"></i>
                                            Add New Position
                                        </a>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th width="15%">Position No.</th>
                                                    <th width="30%">Designation</th>
                                                    <th width="20%">Classification</th>
                                                    <th width="10%">Award</th>
                                                    <th width="10%">For Interview</th>
                                                    <th width="15%">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($positions as $position): ?>
                                                <tr>
                                                    <td class="align-middle"><?= esc($position['position_no']) ?></td>
                                                    <td class="align-middle"><?= esc($position['designation']) ?></td>
                                                    <td class="align-middle"><?= esc($position['classification']) ?></td>
                                                    <td class="align-middle"><?= esc($position['award']) ?></td>
                                                    <td class="align-middle text-center">
                                                        <?php if ($position['for_interview']): ?>
                                                            <span class="badge badge-success"><i class="fas fa-check"></i> Yes</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-secondary"><i class="fas fa-times"></i> No</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="<?= site_url('positions/position/edit/' . $position['id']) ?>"
                                                               class="btn btn-warning btn-sm"
                                                               title="Edit Position">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<?= site_url('positions/position/delete/' . $position['id']) ?>"
                                                               class="btn btn-danger btn-sm"
                                                               onclick="return confirm('Are you sure you want to delete position <?= esc($position['position_no']) ?>?')"
                                                               title="Delete Position">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
