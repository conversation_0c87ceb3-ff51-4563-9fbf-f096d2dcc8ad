<?php

namespace App\Models;

use CodeIgniter\Model;

class InterviewQuestionModel extends Model
{
    protected $table      = 'interview_questions';  // Name of the table
    protected $primaryKey = 'id';                  // Primary key of the table

    protected $useAutoIncrement = true;            // Primary key is auto-incremented

    // Fields that are allowed to be inserted/updated
    protected $allowedFields = [
        'position_id',
        'question_no',
        'question_text',
        'set_score',
        'created_by',
        'updated_by',
        'is_deleted',
        'deleted_at',
        'deleted_by'
    ];

    // Automatically handle the timestamps for created_at and updated_at
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Set up soft deletes to retain deleted records
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';

    // Define default values for specific fields
    protected $defaultValues = [
        'set_score' => 0.00,
        'is_deleted' => false
    ];

    // You can add custom methods here if needed
    
    /**
     * Get questions by position ID
     *
     * @param int $positionId
     * @return array
     */
    public function getQuestionsByPosition($positionId)
    {
        return $this->where('position_id', $positionId)
                    ->where('is_deleted', false)
                    ->orderBy('question_no', 'ASC')
                    ->findAll();
    }
}