<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-star mr-2"></i>
                        Ratings Management
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Ratings</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-layer-group mr-2"></i>
                                Position Groups
                            </h3>
                            
                            <!-- Rate Age Button -->
                            <button type="button" class="btn btn-primary float-right" data-toggle="modal" data-target="#rateAgeModal">
                                <i class="fas fa-birthday-cake mr-1"></i> Rate Age
                            </button>

                            <!-- Rate Age Modal -->
                            <div class="modal fade" id="rateAgeModal" tabindex="-1" role="dialog" aria-labelledby="rateAgeModalLabel" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="rateAgeModalLabel"><i class="fas fa-birthday-cake mr-1"></i> Confirm Rate Age</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p class="text-center">Are you sure you want to rate the age of all the applicants?</p>
                                            <p class="text-center text-muted">This action will apply age ratings to all applicants in the system.</p>
                                            <?= csrf_field() ?>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <button type="button" class="btn btn-primary" id="confirmRateAge">Yes, Rate Ages</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table text-nowrap table-bordered table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Group Name</th>
                                            <th>Description</th>
                                            <th width="15%">Total Applicants</th>
                                            <th width="20%">Rating Status</th>
                                            <th width="15%">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($positionGroups as $group): ?>
                                            <tr class="">
                                                <td class="align-middle"><?= esc($group['group_name']) ?></td>
                                                <td class="align-middle"><?= esc($group['description']) ?></td>
                                                <td class="align-middle text-center">
                                                    <?php
                                                    $totalApplicants = 0;
                                                    foreach ($group['positions'] as $position) {
                                                        $totalApplicants += count($position['applicants'] ?? []);
                                                    }
                                                    echo $totalApplicants;
                                                    ?>
                                                </td>
                                                <td class="align-middle">
                                                    <?php
                                                    $ratedApplicants = 0;
                                                    foreach ($group['positions'] as $position) {
                                                        $ratedApplicants += count(array_filter($position['applicants'] ?? [], function ($applicant) {
                                                            return $applicant['rate_qualification'] != 0;
                                                        }));
                                                    }
                                                    ?>
                                                    <?php if ($totalApplicants > 0): ?>
                                                        <?php if ($ratedApplicants == $totalApplicants): ?>
                                                            <span class="text-success"><i class="fas fa-check-circle"></i> All Rated (<?= $ratedApplicants ?>/<?= $totalApplicants ?>)</span>
                                                        <?php elseif ($ratedApplicants > 0): ?>
                                                            <span class="text-warning"><i class="fas fa-exclamation-circle"></i> Partially Rated (<?= $ratedApplicants ?>/<?= $totalApplicants ?>)</span>
                                                        <?php else: ?>
                                                            <span class="text-danger"><i class="fas fa-times-circle"></i> Not Rated (0/<?= $totalApplicants ?>)</span>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted"><i class="fas fa-info-circle"></i> No Applicants</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="<?= base_url("ratings/viewPositions/{$group['id']}") ?>" class="btn btn-primary btn-sm">
                                                        View Positions <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Add JavaScript code here, inside content section -->
<script>
$(document).ready(function() {
    console.log('Document ready!'); // Debug log
    
    // Configure default Toastr options
    toastr.options = {
        "closeButton": true,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "timeOut": "3000"
    };
    
    $('#confirmRateAge').on('click', function(e) {
        console.log('Rate Age button clicked!'); // Debug log
        e.preventDefault();
        
        // Show loading state
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
        
        // Get CSRF token
        var csrfName = '<?= csrf_token() ?>';
        var csrfHash = $('input[name="<?= csrf_token() ?>"]').val();
        
        console.log('Making AJAX request...'); // Debug log
        
        // Make AJAX call to rate ages
        $.ajax({
            url: '<?= base_url('ratings/rateAges') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                [csrfName]: csrfHash
            },
            success: function(response) {
                console.log('AJAX Success:', response); // Debug log
                
                if (response.success) {
                    // Show success message using Toastr
                    toastr.success(response.message, 'Success!');
                    // Reload the page after a short delay
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    toastr.error(response.message || 'An error occurred while rating ages.', 'Error!');
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', error); // Debug log
                console.log('Status:', status);
                console.log('Response:', xhr.responseText);
                
                // Show error message
                toastr.error('An error occurred while processing the request.', 'Error!');
            },
            complete: function() {
                console.log('AJAX request completed'); // Debug log
                
                // Reset button state
                $('#confirmRateAge').prop('disabled', false).html('Yes, Rate Ages');
                // Close the modal
                $('#rateAgeModal').modal('hide');
            }
        });
    });
});
</script>

<?= $this->endSection() ?>