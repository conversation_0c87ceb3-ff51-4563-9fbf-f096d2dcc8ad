<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Positions in <?= esc($groupName) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item active">View Positions</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Positions</h3>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Position No.</th>
                                        <th>Designation</th>
                                        <th>Classification</th>
                                        <th>Total Applicants</th>
                                        <th>Rated Applicants</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positions as $position): ?>
                                    <tr>
                                        <td><?= esc($position['position_no']) ?></td>
                                        <td><?= esc($position['designation']) ?></td>
                                        <td><?= esc($position['classification']) ?></td>
                                        <td><?= $position['applicants_count'] ?></td>
                                        <td><?= $position['rated_applicants_count'] ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?= base_url("reports/applicantProfiles/{$position['id']}") ?>" class="btn btn-primary btn-sm">View (RS 3.7)</a>
                                                
                                                <a href="<?= base_url("reports/listApplicantsByRank/{$groupId}/{$position['id']}") ?>" class="btn btn-info btn-sm">By Rank</a>
                                                <a href="<?= base_url("reports/analysisReport/{$groupId}/{$position['id']}") ?>" class="btn btn-success btn-sm">Analysis Report</a>
                                                <?php if ($position['for_interview'] == 1): ?>
                                                <a href="<?= base_url("reports/interviewList/{$position['id']}") ?>" class="btn btn-warning btn-sm">Interview List</a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
