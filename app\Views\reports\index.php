<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-chart-bar mr-2"></i>
                        Reports Dashboard
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Reports</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Summary Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3><?= $totalApplicants ?></h3>
                            <p>Total Applicants</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3><?= $totalRatedApplicants ?></h3>
                            <p>Rated Applicants</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3><?= $totalPositions ?></h3>
                            <p>Total Positions</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3><?= $totalRatedPositions ?></h3>
                            <p>Rated Positions</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check-double"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Eliminated/Shortlisted Summary -->
            <div class="row">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-danger"><i class="fas fa-user-times"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Total Eliminated</span>
                            <span class="info-box-number"><?= $totalEliminatedCount ?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-user-check"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Total Shortlisted</span>
                            <span class="info-box-number"><?= $totalShortlistedCount ?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning"><i class="fas fa-user-slash"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Total Withdrawn</span>
                            <span class="info-box-number"><?= $totalWithdrawnCount ?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-secondary"><i class="fas fa-question-circle"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">No Status</span>
                            <span class="info-box-number"><?= $totalBlankStatusCount ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rating Progress -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Overall Rating Progress</h3>
                        </div>
                        <div class="card-body">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: <?= $ratingProgress ?>%;" aria-valuenow="<?= $ratingProgress ?>" aria-valuemin="0" aria-valuemax="100"><?= number_format($ratingProgress, 1) ?>%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Applicants per Position Group</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="applicantsPerGroupChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Rating Distribution</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="ratingDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Applicants per Position Group Chart
                var applicantsPerGroupCtx = document.getElementById('applicantsPerGroupChart').getContext('2d');
                new Chart(applicantsPerGroupCtx, {
                    type: 'bar',
                    data: {
                        labels: <?= json_encode(array_column($positionGroups, 'group_name')) ?>,
                        datasets: [{
                            label: 'Number of Applicants',
                            data: <?= json_encode(array_column($positionGroups, 'all_applicants_count')) ?>,
                            backgroundColor: 'rgba(60,141,188,0.8)',
                            borderColor: 'rgba(60,141,188,1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Rating Distribution Chart
                var ratingDistributionCtx = document.getElementById('ratingDistributionChart').getContext('2d');
                new Chart(ratingDistributionCtx, {
                    type: 'pie',
                    data: {
                        labels: ['Excellent (80-100)', 'Good (60-79)', 'Average (40-59)', 'Below Average (20-39)', 'Poor (0-19)'],
                        datasets: [{
                            data: <?= json_encode($ratingDistribution) ?>,
                            backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#fd7e14', '#dc3545']
                        }]
                    },
                    options: {
                        responsive: true
                    }
                });
            });
            </script>

            <!-- Position Groups Table -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-layer-group mr-2"></i>
                        Position Groups
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('reports/preSelectionReport') ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-file-alt mr-1"></i> Pre-Selection (RS 3.7A)
                        </a>
                        <a href="<?= base_url('reports/interviewSchedule') ?>" class="btn btn-success btn-sm ml-2">
                            <i class="fas fa-calendar-alt mr-1"></i> Interview Schedule
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover">
                            <thead class="thead-dark">
                                <tr class="">
                                    <th>Group Name</th>
                                    <th>Total Positions</th>
                                    <th>Rated Positions</th>
                                    <th>Total Applicants</th>
                                    <th>Rated Applicants</th>
                                    <th>Status Counts
                                        <div class="small text-white-50">
                                            <i class="fas fa-user-times text-danger" title="Eliminated"></i> Eliminated
                                            <i class="fas fa-user-check text-success ml-2" title="Shortlisted"></i> Shortlisted<br>
                                            <i class="fas fa-user-slash text-warning" title="Withdrawn"></i> Withdrawn
                                            <i class="fas fa-question-circle text-secondary ml-2" title="No Status"></i> No Status
                                        </div>
                                    </th>
                                    <th width="15%">Rating Progress</th>
                                    <th width="15%">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($positionGroups as $group): ?>
                                <tr class="">
                                    <td class="align-middle"><?= esc($group['group_name']) ?></td>
                                    <td class="align-middle text-center"><?= count($group['positions']) ?></td>
                                    <td class="align-middle text-center"><?= $group['rated_positions'] ?></td>
                                    <td class="align-middle text-center"><?= $group['all_applicants_count'] ?></td>
                                    <td class="align-middle text-center"><?= $group['total_rated_applicants'] ?></td>
                                    <td class="align-middle text-center">
                                        <?php
                                        $eliminated = isset($group['eliminated_count']) ? $group['eliminated_count'] : 0;
                                        $shortlisted = isset($group['shortlisted_count']) ? $group['shortlisted_count'] : 0;
                                        $withdrawn = isset($group['withdrawn_count']) ? $group['withdrawn_count'] : 0;
                                        $blank = isset($group['blank_status_count']) ? $group['blank_status_count'] : 0;
                                        ?>
                                        <div class="small">
                                            <table class="table table-sm mb-0">
                                                <tr>
                                                    <td class="border-0 p-0 pr-2 text-right"><i class="fas fa-user-times text-danger"></i></td>
                                                    <td class="border-0 p-0 text-left"><?= $eliminated ?></td>
                                                    <td class="border-0 p-0 pr-2 text-right"><i class="fas fa-user-check text-success"></i></td>
                                                    <td class="border-0 p-0 text-left"><?= $shortlisted ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="border-0 p-0 pr-2 text-right"><i class="fas fa-user-slash text-warning"></i></td>
                                                    <td class="border-0 p-0 text-left"><?= $withdrawn ?></td>
                                                    <td class="border-0 p-0 pr-2 text-right"><i class="fas fa-question-circle text-secondary"></i></td>
                                                    <td class="border-0 p-0 text-left"><?= $blank ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: <?= $group['rating_progress'] ?>%;" 
                                                 aria-valuenow="<?= $group['rating_progress'] ?>" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                <?= number_format($group['rating_progress'], 1) ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <a href="<?= base_url("reports/viewPositions/{$group['id']}") ?>" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-eye mr-1"></i> View Report
                                            </a>
                                            <?php if ($group['needs_generation']): ?>
                                                <a href="<?= base_url("applicants/generateTotal/{$group['id']}") ?>" 
                                                   class="btn btn-warning btn-sm">
                                                    <i class="fas fa-sync mr-1"></i> Generate Total
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Add a container to display the results -->
            <div id="totalResults" style="display: none;">
                <!-- Results will be inserted here -->
            </div>

            
        </div>
    </section>
</div>
<?= $this->endSection() ?>
