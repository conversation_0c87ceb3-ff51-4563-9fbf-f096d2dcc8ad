<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('styles') ?>
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-file-alt mr-2"></i>
                        Pre-Selection Report Editor
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('settings') ?>">Settings</a></li>
                        <li class="breadcrumb-item active">Pre-Selection Report</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">Edit Pre-Selection Report Content</h3>
                    <div class="card-tools">
                        <span id="save-status" class="text-muted" style="display: none;">
                            <i class="fas fa-save mr-1"></i> Saving...
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <textarea id="culling-editor" class="form-control"><?= esc($org['culling']) ?></textarea>
                </div>
            </div>
            
            <!-- Add Signatures Section -->
            <div class="card card-outline card-primary mt-4">
                <div class="card-header">
                    <h3 class="card-title">Committee Signatures</h3>
                    <div class="card-tools">
                        <span id="signatures-save-status" class="text-muted" style="display: none;">
                            <i class="fas fa-save mr-1"></i> Saving...
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <textarea id="signatures-editor" class="form-control"><?= esc($org['composition']) ?></textarea>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(function() {
    // Initialize Summernote with full features except image and video
    if (typeof $.fn.summernote === 'undefined') {
        // Load Summernote if not already loaded
        $.getScript('https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js', function() {
            initSummernote();
        });
    } else {
        initSummernote();
    }

    function initSummernote() {
        // Initialize the culling editor
        $('#culling-editor').summernote({
            height: 500,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks: {
                onChange: function() {
                    // Clear previous timeout
                    if (window.saveTimeoutCulling) {
                        clearTimeout(window.saveTimeoutCulling);
                    }
                    
                    // Show saving status
                    $('#save-status').show();
                    
                    // Set new timeout for auto-save
                    window.saveTimeoutCulling = setTimeout(function() {
                        const content = $('#culling-editor').summernote('code');
                        
                        // Make AJAX call to save content
                        $.ajax({
                            url: '<?= base_url('settings/save_pre_selection_report') ?>',
                            type: 'POST',
                            data: {
                                culling: content,
                                '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $('#save-status').html('<i class="fas fa-check mr-1"></i> Saved').removeClass('text-muted').addClass('text-success');
                                    setTimeout(() => {
                                        $('#save-status').fadeOut();
                                    }, 2000);
                                } else {
                                    $('#save-status').html('<i class="fas fa-times mr-1"></i> Error saving').removeClass('text-muted').addClass('text-danger');
                                }
                            },
                            error: function() {
                                $('#save-status').html('<i class="fas fa-times mr-1"></i> Error saving').removeClass('text-muted').addClass('text-danger');
                            }
                        });
                    }, 1000); // Save after 1 second of inactivity
                }
            }
        });
        
        // Initialize the signatures editor
        $('#signatures-editor').summernote({
            height: 300,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks: {
                onChange: function() {
                    // Clear previous timeout
                    if (window.saveTimeoutSignatures) {
                        clearTimeout(window.saveTimeoutSignatures);
                    }
                    
                    // Show saving status
                    $('#signatures-save-status').show();
                    
                    // Set new timeout for auto-save
                    window.saveTimeoutSignatures = setTimeout(function() {
                        const content = $('#signatures-editor').summernote('code');
                        
                        // Make AJAX call to save content
                        $.ajax({
                            url: '<?= base_url('settings/save_signatures') ?>',
                            type: 'POST',
                            data: {
                                composition: content,
                                '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $('#signatures-save-status').html('<i class="fas fa-check mr-1"></i> Saved').removeClass('text-muted').addClass('text-success');
                                    setTimeout(() => {
                                        $('#signatures-save-status').fadeOut();
                                    }, 2000);
                                } else {
                                    $('#signatures-save-status').html('<i class="fas fa-times mr-1"></i> Error saving').removeClass('text-muted').addClass('text-danger');
                                }
                            },
                            error: function() {
                                $('#signatures-save-status').html('<i class="fas fa-times mr-1"></i> Error saving').removeClass('text-muted').addClass('text-danger');
                            }
                        });
                    }, 1000); // Save after 1 second of inactivity
                }
            }
        });
    }
});
</script>
<?= $this->endSection() ?> 