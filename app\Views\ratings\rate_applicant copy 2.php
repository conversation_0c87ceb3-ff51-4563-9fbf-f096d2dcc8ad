<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Rate Applicant: <?= esc($applicant['name']) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('ratings') ?>">Ratings</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("ratings/viewPositions/{$position['position_group_id']}") ?>">View Positions</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("ratings/viewApplicants/{$position['id']}") ?>">View Applicants</a></li>
                        <li class="breadcrumb-item active">Rate Applicant</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">

            <div class="row">
                <div class="col-12 p-3">
                    <button type="button" class="btn bg-purple float-right btn-copy-details" onclick="copyAllDetails()">
                        <i class="fas fa-copy"></i> AI Prompt
                    </button>
                </div>
                <script>
                    function copyAllDetails() {
                        const positionDiv = document.getElementById('position-details');
                        const applicantDiv = document.getElementById('applicant-details');
                        const rateDiv = document.getElementById('rate-applicant-form');

                        const text = "INSTRUCTIONS:\n" +
                            "Compare the applicant specifications against the position specifications and rate this applicant based on the following criteria:\n\n" +
                            "Start off analysis write up with this: This is the rating analysis for the applicant: <?= esc($applicant['name']) ?> against the position: <?= esc($position['designation']) ?> \n\n " +
                            "RATING CRITERIA:\n" +
                            "================\n" +
                            "1. Age\n" +
                            "2. Education Qualification\n" + 
                            "3. Capability\n" +
                            "4. Public Servant Status\n" +
                            "5. Skills and Competencies\n" +
                            "6. Work Experience/Employment History:\n" +
                            "7. Private Sector (Non-Relevant)\n" +
                            "8. Private Sector (Relevant)\n" +
                            "9. Public Service (Non-Relevant)\n" +
                            "10. Public Service (Relevant)\n" +
                            "11. Knowledge\n" +
                            "12. Training Relevance\n\n" +
                            "Please provide brief remarks after rating.\n\n" +
                            "POSITION DETAILS:\n" +
                            "================\n" +
                            positionDiv.innerText +
                            "\n\nAPPLICANT DETAILS:\n" +
                            "================\n" +
                            applicantDiv.innerText +
                            "\n\nRATING FORM:\n" +
                            "================\n" +
                            rateDiv.innerText;

                        navigator.clipboard.writeText(text).then(() => {
                            // Show success feedback
                            const btn = document.querySelector('.btn-copy-details');
                            const originalText = btn.innerHTML;
                            btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                            setTimeout(() => {
                                btn.innerHTML = originalText;
                            }, 2000);
                        }).catch(err => {
                            console.error('Failed to copy text: ', err);
                        });
                    }
                </script>

            </div>

            <div class="row">
                <div class="col-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-briefcase mr-2"></i>
                                Position Specifications
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="position-details">
                                <h4><?= esc($position['designation']) ?></h4>
                                <p><strong>Position No.:</strong> <?= esc($position['position_no']) ?></p>
                                <p><strong>Classification:</strong> <?= esc($position['classification']) ?></p>
                                <p><strong>Qualifications:</strong> <?= nl2br(esc($position['qualifications'])) ?></p>
                                <p><strong>Knowledge:</strong> <?= nl2br(esc($position['knowledge'])) ?></p>
                                <p><strong>Skills and Competencies:</strong> <?= nl2br(esc($position['skills_competencies'])) ?></p>
                                <p><strong>Job Experiences:</strong> <?= nl2br(esc($position['job_experiences'])) ?></p>
                            </div>
                        </div>
                        <script>
                            function copyPositionDetails() {
                                const detailsDiv = document.getElementById('position-details');
                                const text = detailsDiv.innerText;

                                navigator.clipboard.writeText(text).then(() => {
                                    // Show success feedback
                                    const btn = document.querySelector('.copy-position-details-btn');
                                    const originalText = btn.innerHTML;
                                    btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                                    setTimeout(() => {
                                        btn.innerHTML = originalText;
                                    }, 2000);
                                }).catch(err => {
                                    console.error('Failed to copy text: ', err);
                                });
                            }
                        </script>
                    </div>
                </div>

                <div class="col-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user mr-2"></i>
                                Applicant Specifications
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="applicant-details">
                                <h4><?= esc($applicant['name']) ?></h4>
                                <p><strong>Sex:</strong> <?= esc($applicant['sex']) ?></p>
                                <p><strong>Age:</strong> <?= esc($applicant['age']) ?></p>
                                <p><strong>Place of Origin:</strong> <?= esc($applicant['place_origin']) ?></p>
                                <p><strong>Current Employer:</strong> <?= esc($applicant['current_employer']) ?></p>
                                <p><strong>Current Position:</strong> <?= esc($applicant['current_position']) ?></p>
                                <p><strong>Address/Location:</strong> <?= esc($applicant['address_location']) ?></p>
                                <p><strong>Contact Details:</strong> <?= esc($applicant['contact_details']) ?></p>
                                <p><strong>NID Number:</strong> <?= esc($applicant['nid_number']) ?></p>
                                <p><strong>Qualification:</strong> <?= nl2br(esc($applicant['qualification_text'])) ?></p>
                                <p><strong>Other Trainings:</strong> <?= nl2br(esc($applicant['other_trainings'])) ?></p>
                                <p><strong>Knowledge:</strong> <?= nl2br(esc($applicant['knowledge'])) ?></p>
                                <p><strong>Skills and Competencies:</strong> <?= nl2br(esc($applicant['skills_competencies'])) ?></p>
                                <p><strong>Job Experiences:</strong> <?= nl2br(esc($applicant['job_experiences'])) ?></p>
                                <p><strong>Publications:</strong> <?= nl2br(esc($applicant['publications'])) ?></p>
                                <p><strong>Awards:</strong> <?= nl2br(esc($applicant['awards'])) ?></p>
                                <p><strong>Referees:</strong> <?= nl2br(esc($applicant['referees'])) ?></p>
                                <p><strong>Comments:</strong> <?= nl2br(esc($applicant['comments'])) ?></p>
                            </div>
                        </div>
                        <script>
                            function copyApplicantDetails() {
                                const detailsDiv = document.getElementById('applicant-details');
                                const text = detailsDiv.innerText;

                                navigator.clipboard.writeText(text).then(() => {
                                    // Show success feedback
                                    const btn = document.querySelector('.copy-applicant-details-btn');
                                    const originalText = btn.innerHTML;
                                    btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                                    setTimeout(() => {
                                        btn.innerHTML = originalText;
                                    }, 2000);
                                }).catch(err => {
                                    console.error('Failed to copy text: ', err);
                                });
                            }
                        </script>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-star mr-2"></i>
                                Rate Applicant
                            </h3>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url("ratings/rateApplicant/{$applicant['id']}") ?>" method="post">
                                <div class="row" id="rate-applicant-form">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="rate_age"><strong>Age</strong></label>
                                            <select class="form-control " id="rate_age" name="rate_age" required>
                                                <option value="">Select Age Range</option>
                                                <option value="0" <?= ($applicant['rate_age'] == '0') ? 'selected' : '' ?>>65+yrs | 0</option>
                                                <option value="1" <?= ($applicant['rate_age'] == '1') ? 'selected' : '' ?>>60-64yrs | 1</option>
                                                <option value="2" <?= ($applicant['rate_age'] == '2') ? 'selected' : '' ?>>54-59yrs | 2</option>
                                                <option value="3" <?= ($applicant['rate_age'] == '3') ? 'selected' : '' ?>>48-53yrs | 3</option>
                                                <option value="4" <?= ($applicant['rate_age'] == '4') ? 'selected' : '' ?>>42-47yrs | 4</option>
                                                <option value="5" <?= ($applicant['rate_age'] == '5') ? 'selected' : '' ?>>36-41yrs | 5</option>
                                                <option value="6" <?= ($applicant['rate_age'] == '6') ? 'selected' : '' ?>>30-35yrs | 6</option>
                                                <option value="7" <?= ($applicant['rate_age'] == '7') ? 'selected' : '' ?>>24-29yrs | 7</option>
                                                <option value="8" <?= ($applicant['rate_age'] == '8') ? 'selected' : '' ?>>18-23yrs | 8</option>
                                            </select>
                                            <input type="hidden" name="max_rate_age" value="8">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_education"><strong>Education Qualification</strong></label>
                                            <select class="form-control " id="rate_education" name="rate_education" required>
                                                <option value="">Select Education Range</option>
                                                <option value="10" <?= ($applicant['rate_qualification'] == '10') ? 'selected' : '' ?>>Doctorate | 10</option>
                                                <option value="9" <?= ($applicant['rate_qualification'] == '9') ? 'selected' : '' ?>>Master | 9</option>
                                                <option value="8" <?= ($applicant['rate_qualification'] == '8') ? 'selected' : '' ?>>Honors/Post.Grad/Cert/Diploma | 8</option>
                                                <option value="7" <?= ($applicant['rate_qualification'] == '7') ? 'selected' : '' ?>>Bachelors Degree | 7</option>
                                                <option value="6" <?= ($applicant['rate_qualification'] == '6') ? 'selected' : '' ?>>Adv.Diploma/Associate Degree | 6</option>
                                                <option value="5" <?= ($applicant['rate_qualification'] == '5') ? 'selected' : '' ?>>Diploma | 5</option>
                                                <option value="4" <?= ($applicant['rate_qualification'] == '4') ? 'selected' : '' ?>>Cert.4/Certificate (High.Edu) | 4</option>
                                                <option value="3" <?= ($applicant['rate_qualification'] == '3') ? 'selected' : '' ?>>Cert.3 | 3</option>
                                                <option value="2" <?= ($applicant['rate_qualification'] == '2') ? 'selected' : '' ?>>Cert.2 | 2</option>
                                                <option value="1" <?= ($applicant['rate_qualification'] == '1') ? 'selected' : '' ?>>Cert.1 | 1</option>
                                            </select>
                                            <input type="hidden" name="max_rate_education" value="10">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_capability"><strong>Capability Rating</strong></label>
                                            <select class="form-control " id="rate_capability" name="rate_capability" required>
                                                <option value="">Select Capability Level</option>
                                                <option value="5" <?= ($applicant['rate_capability'] == '5') ? 'selected' : '' ?>>Very Capable | 5</option>
                                                <option value="4" <?= ($applicant['rate_capability'] == '4') ? 'selected' : '' ?>>Above Average | 4</option>
                                                <option value="3" <?= ($applicant['rate_capability'] == '3') ? 'selected' : '' ?>>Average/Competent | 3</option>
                                                <option value="2" <?= ($applicant['rate_capability'] == '2') ? 'selected' : '' ?>>Low Capability | 2</option>
                                                <option value="1" <?= ($applicant['rate_capability'] == '1') ? 'selected' : '' ?>>No Capability | 1</option>
                                            </select>
                                            <input type="hidden" name="max_rate_capability" value="5">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_public_service"><strong>Public Service Status</strong></label>
                                            <select class="form-control " id="rate_public_service" name="rate_public_service" required>
                                                <option value="">Select Status</option>
                                                <option value="3" <?= ($applicant['rate_public_service'] == '3') ? 'selected' : '' ?>>Section 39 | 3</option>
                                                <option value="2" <?= ($applicant['rate_public_service'] == '2') ? 'selected' : '' ?>>Public Servant | 2</option>
                                                <option value="1" <?= ($applicant['rate_public_service'] == '1') ? 'selected' : '' ?>>Non-Public Servant | 1</option>
                                            </select>
                                            <input type="hidden" name="max_rate_public_service" value="3">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_skills_competencies"><strong>Skills and Competencies Rating</strong></label>
                                            <select class="form-control " id="rate_skills_competencies" name="rate_skills_competencies" required>
                                                <option value="">Select Skills and Competencies Level</option>
                                                <option value="5" <?= ($applicant['rate_skills_competencies'] == '5') ? 'selected' : '' ?>>Exceptional Skills | 5</option>
                                                <option value="4" <?= ($applicant['rate_skills_competencies'] == '4') ? 'selected' : '' ?>>Above Average Skills | 4</option>
                                                <option value="3" <?= ($applicant['rate_skills_competencies'] == '3') ? 'selected' : '' ?>>Average Skills | 3</option>
                                                <option value="2" <?= ($applicant['rate_skills_competencies'] == '2') ? 'selected' : '' ?>>Below Average Skills | 2</option>
                                                <option value="1" <?= ($applicant['rate_skills_competencies'] == '1') ? 'selected' : '' ?>>Limited Skills | 1</option>
                                                <option value="0" <?= ($applicant['rate_skills_competencies'] == '0') ? 'selected' : '' ?>>No Skills | 0</option>
                                            </select>
                                            <input type="hidden" name="max_rate_skills_competencies" value="5">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="rate_private_non_relevant"><strong>Private Sector Work Experience (Non-Relevant)</strong></label>
                                            <select class="form-control" id="rate_private_non_relevant" name="rate_private_non_relevant" required>
                                                <option value="">Select Prv. NON-RELEVANT Experience</option>
                                                <option value="4" <?= ($applicant['rate_private_non_relevant'] == '4') ? 'selected' : '' ?>>Prv. Non-Relevant 20+ yrs | 4</option>
                                                <option value="3" <?= ($applicant['rate_private_non_relevant'] == '3') ? 'selected' : '' ?>>Prv. Non-Relevant 15-19yrs | 3</option>
                                                <option value="2" <?= ($applicant['rate_private_non_relevant'] == '2') ? 'selected' : '' ?>>Prv. Non-Relevant 10-14yrs | 2</option>
                                                <option value="1" <?= ($applicant['rate_private_non_relevant'] == '1') ? 'selected' : '' ?>>Prv. Non-Relevant 5-9yrs | 1</option>
                                                <option value="0" <?= ($applicant['rate_private_non_relevant'] == '0') ? 'selected' : '' ?>>No/Less Non-Relevant Xp. | 0</option>
                                            </select>
                                            <input type="hidden" name="max_rate_private_non_relevant" value="4">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_private_relevant"><strong>Private Sector Work Experience (Relevant)</strong></label>
                                            <select class="form-control " id="rate_private_relevant" name="rate_private_relevant" required>
                                                <option value="">Select Prv. RELEVANT Experience</option>
                                                <option value="5" <?= ($applicant['rate_private_relevant'] == '5') ? 'selected' : '' ?>>Prv. Relevant 20+ yrs | 5</option>
                                                <option value="4" <?= ($applicant['rate_private_relevant'] == '4') ? 'selected' : '' ?>>Prv. Relevant 15-19yrs | 4</option>
                                                <option value="3" <?= ($applicant['rate_private_relevant'] == '3') ? 'selected' : '' ?>>Prv. Relevant 10-14yrs | 3</option>
                                                <option value="2" <?= ($applicant['rate_private_relevant'] == '2') ? 'selected' : '' ?>>Prv. Relevant 5-9yrs | 2</option>
                                                <option value="1" <?= ($applicant['rate_private_relevant'] == '1') ? 'selected' : '' ?>>Prv. Relevant 1-4yrs | 1</option>
                                                <option value="0" <?= ($applicant['rate_private_relevant'] == '0') ? 'selected' : '' ?>>Prv. No Relevant Xp. | 0</option>
                                            </select>
                                            <input type="hidden" name="max_rate_private_relevant" value="5">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_public_non_relevant"><strong>P.S Non-Relevant Work Experience</strong></label>
                                            <select class="form-control " id="rate_public_non_relevant" name="rate_public_non_relevant" required>
                                                <option value="">Select PS. NON-RELEVANT Experience</option>
                                                <option value="5" <?= ($applicant['rate_public_non_relevant'] == '5') ? 'selected' : '' ?>>PS. Non-Relevant 20+ yrs | 5</option>
                                                <option value="4" <?= ($applicant['rate_public_non_relevant'] == '4') ? 'selected' : '' ?>>PS. Non-Relevant 15-19yrs | 4</option>
                                                <option value="3" <?= ($applicant['rate_public_non_relevant'] == '3') ? 'selected' : '' ?>>PS. Non-Relevant 10-14yrs | 3</option>
                                                <option value="2" <?= ($applicant['rate_public_non_relevant'] == '2') ? 'selected' : '' ?>>PS. Non-Relevant 5-9yrs | 2</option>
                                                <option value="1" <?= ($applicant['rate_public_non_relevant'] == '1') ? 'selected' : '' ?>>PS. Non-Relevant 1-4yrs | 1</option>
                                                <option value="0" <?= ($applicant['rate_public_non_relevant'] == '0') ? 'selected' : '' ?>>PS. No Experience | 0</option>
                                            </select>
                                            <input type="hidden" name="max_rate_public_non_relevant" value="5">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_public_relevant"><strong>P.S Relevant Work Experience</strong></label>
                                            <select class="form-control" id="rate_public_relevant" name="rate_public_relevant" required>
                                                <option value="">Select PS. RELEVANT Experience</option>
                                                <option value="6" <?= ($applicant['rate_public_relevant'] == '6') ? 'selected' : '' ?>>PS. Relevant 20+ yrs | 6</option>
                                                <option value="5" <?= ($applicant['rate_public_relevant'] == '5') ? 'selected' : '' ?>>PS. Relevant 15-19yrs | 5</option>
                                                <option value="4" <?= ($applicant['rate_public_relevant'] == '4') ? 'selected' : '' ?>>PS. Relevant 10-14yrs | 4</option>
                                                <option value="3" <?= ($applicant['rate_public_relevant'] == '3') ? 'selected' : '' ?>>PS. Relevant 5-9yrs | 3</option>
                                                <option value="2" <?= ($applicant['rate_public_relevant'] == '2') ? 'selected' : '' ?>>PS. Relevant 1-4yrs | 2</option>
                                                <option value="0" <?= ($applicant['rate_public_relevant'] == '0') ? 'selected' : '' ?>>PS. No Experience | 0</option>
                                            </select>
                                            <input type="hidden" name="max_rate_public_relevant" value="6">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_knowledge"><strong>Knowledge Rating</strong></label>
                                            <select class="form-control " id="rate_knowledge" name="rate_knowledge" required>
                                                <option value="">Select Knowledge Level</option>
                                                <option value="5" <?= ($applicant['rate_knowledge'] == '5') ? 'selected' : '' ?>>Expert Knowledge | 5</option>
                                                <option value="4" <?= ($applicant['rate_knowledge'] == '4') ? 'selected' : '' ?>>Advanced Knowledge | 4</option>
                                                <option value="3" <?= ($applicant['rate_knowledge'] == '3') ? 'selected' : '' ?>>Intermediate Knowledge | 3</option>
                                                <option value="2" <?= ($applicant['rate_knowledge'] == '2') ? 'selected' : '' ?>>Basic Knowledge | 2</option>
                                                <option value="1" <?= ($applicant['rate_knowledge'] == '1') ? 'selected' : '' ?>>Limited Knowledge | 1</option>
                                                <option value="0" <?= ($applicant['rate_knowledge'] == '0') ? 'selected' : '' ?>>No Knowledge | 0</option>
                                            </select>
                                            <input type="hidden" name="max_rate_knowledge" value="5">
                                        </div>
                                        <div class="form-group">
                                            <label for="rate_trainings"><strong>Training Relevance Rating</strong></label>
                                            <select class="form-control " id="rate_trainings" name="rate_trainings" required data-placeholder="Select Training Relevance">
                                                <option value="">Select Training Relevance</option>
                                                <option value="5" <?= ($applicant['rate_trainings'] == '5') ? 'selected' : '' ?>>Highly relevant to the position | 5</option>
                                                <option value="4" <?= ($applicant['rate_trainings'] == '4') ? 'selected' : '' ?>>Very relevant to the position | 4</option>
                                                <option value="3" <?= ($applicant['rate_trainings'] == '3') ? 'selected' : '' ?>>Moderately relevant to the position | 3</option>
                                                <option value="2" <?= ($applicant['rate_trainings'] == '2') ? 'selected' : '' ?>>Somewhat relevant to the position | 2</option>
                                                <option value="1" <?= ($applicant['rate_trainings'] == '1') ? 'selected' : '' ?>>Slightly relevant to the position | 1</option>
                                                <option value="0" <?= ($applicant['rate_trainings'] == '0') ? 'selected' : '' ?>>Not relevant to the position | 0</option>
                                            </select>
                                            <input type="hidden" name="max_rate_trainings" value="5">
                                        </div>

                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Criteria</th>
                                                    <th>Rating</th>
                                                    <th>Out of</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Age</td>
                                                    <td><?= $applicant['rate_age'] ?? 0 ?></td>
                                                    <td><?= $applicant['max_rate_age'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Qualification</td>
                                                    <td><?= $applicant['rate_qualification'] ?? 0 ?></td>
                                                    <td><?= $applicant['max_rate_qualification'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Experience</td>
                                                    <td>
                                                        <?=
                                                        ($applicant['rate_private_non_relevant'] ?? 0) +
                                                            ($applicant['rate_private_relevant'] ?? 0) +
                                                            ($applicant['rate_public_non_relevant'] ?? 0) +
                                                            ($applicant['rate_public_relevant'] ?? 0) ?>
                                                    </td>
                                                    <td>
                                                        <?=
                                                        ($applicant['max_rate_private_non_relevant'] ?? 0) +
                                                            ($applicant['max_rate_private_relevant'] ?? 0) +
                                                            ($applicant['max_rate_public_non_relevant'] ?? 0) +
                                                            ($applicant['max_rate_public_relevant'] ?? 0) ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Trainings</td>
                                                    <td><?= $applicant['rate_trainings'] ?? 0 ?></td>
                                                    <td><?= $applicant['max_rate_trainings'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Skills and Competencies</td>
                                                    <td><?= $applicant['rate_skills_competencies'] ?? 0 ?></td>
                                                    <td><?= $applicant['max_rate_skills_competencies'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Knowledge</td>
                                                    <td><?= $applicant['rate_knowledge'] ?? 0 ?></td>
                                                    <td><?= $applicant['max_rate_knowledge'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Capability</td>
                                                    <td><?= $applicant['rate_capability'] ?? 0 ?></td>
                                                    <td><?= $applicant['max_rate_capability'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Public Service Status</td>
                                                    <td><?= $applicant['rate_public_service'] ?? 0 ?></td>
                                                    <td><?= $applicant['max_rate_public_service'] ?? 0 ?></td>
                                                </tr>
                                                <tr class="text-bold bg-secondary bg-opacity-10">
                                                    <td>Total</td>
                                                    <td><?=
                                                        ($applicant['rate_age'] ?? 0) +
                                                            ($applicant['rate_qualification'] ?? 0) +
                                                            //($applicant['rate_experience'] ?? 0) +
                                                            ($applicant['rate_trainings'] ?? 0) +
                                                            ($applicant['rate_skills_competencies'] ?? 0) +
                                                            ($applicant['rate_knowledge'] ?? 0) +
                                                            ($applicant['rate_public_service'] ?? 0) +
                                                            ($applicant['rate_private_non_relevant'] ?? 0) +
                                                            ($applicant['rate_private_relevant'] ?? 0) +
                                                            ($applicant['rate_public_non_relevant'] ?? 0) +
                                                            ($applicant['rate_public_relevant'] ?? 0) +
                                                            ($applicant['rate_capability'] ?? 0)
                                                        ?></td>
                                                    <td><?=
                                                        ($applicant['max_rate_age'] ?? 0) +
                                                            ($applicant['max_rate_qualification'] ?? 0) +
                                                            //($applicant['max_rate_experience'] ?? 0) +
                                                            ($applicant['max_rate_trainings'] ?? 0) +
                                                            ($applicant['max_rate_skills_competencies'] ?? 0) +
                                                            ($applicant['max_rate_knowledge'] ?? 0) +
                                                            ($applicant['max_rate_public_service'] ?? 0) +
                                                            ($applicant['max_rate_private_non_relevant'] ?? 0) +
                                                            ($applicant['max_rate_private_relevant'] ?? 0) +
                                                            ($applicant['max_rate_public_non_relevant'] ?? 0) +
                                                            ($applicant['max_rate_public_relevant'] ?? 0) +
                                                            ($applicant['max_rate_capability'] ?? 0)
                                                        ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="remarks">Remarks</label>
                                    <textarea class="form-control" id="remarks" name="remarks" rows="5" placeholder="Write Remarks"><?= $applicant['remarks'] ?? '' ?></textarea>
                                </div>
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">Submit Rating</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
