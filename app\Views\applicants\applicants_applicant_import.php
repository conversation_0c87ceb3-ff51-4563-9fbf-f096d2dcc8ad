<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('applicants') ?>">Position Groups</a></li>
                        <li class="breadcrumb-item active">Import Applicant</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="card-title">Import Applicant to <?= esc($position['designation']) ?></h3>
                                <a href="<?= site_url('applicants/list/' . $position['id']) ?>" class="btn btn-default">
                                    <i class="fas fa-arrow-left"></i> Back to Applicants List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <form action="<?= site_url('applicants/processApplicantImport/' . $position['id']) ?>" method="post">
                                <div class="form-group">
                                    <label for="applicant_search">Search Applicant</label>
                                    <select class="form-control select2bs4" id="applicant_search" name="applicant_id" required>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">Import Applicant</button>
                                <a href="<?= site_url('applicants/list/' . $position['id']) ?>" class="btn btn-secondary">Cancel</a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    $('#applicant_search').select2({
        theme: 'bootstrap4',
        ajax: {
            url: '<?= site_url('applicants/searchApplicants') ?>',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    term: params.term,
                    position_id: <?= $position['id'] ?>
                };
            },
            processResults: function(data) {
                return data;
            },
            cache: true
        },
        minimumInputLength: 2,
        placeholder: 'Search for an applicant...',
        allowClear: true
    });
});
</script>
<?= $this->endSection() ?>