<?php

namespace App\Models;

use CodeIgniter\Model;

class PositionModel extends Model
{
    protected $table      = 'positions';        // Name of the table
    protected $primaryKey = 'id';               // Primary key of the table

    protected $useAutoIncrement = true;         // Primary key is auto-incremented

    // Fields that are allowed to be inserted/updated
    protected $allowedFields = [
        'position_no',
        'designation',
        'classification',
        'award',
        'qualifications',
        'knowledge',
        'skills_competencies',
        'job_experiences',
        'org_id',
        'is_active',
        'for_interview',
        'created_by',
        'updated_by',
        'position_group_id',
        'remarks'
    ];

    // Automatically handle the timestamps for created_at and updated_at
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Optional: You can set up soft deletes if you want to retain inactive records
    protected $useSoftDeletes = false;

    // Optional: Define default values for specific fields (e.g., is_active)
    protected $defaultValues = [
        'is_active' => 1
    ];

    // You can add custom methods here if needed
}
