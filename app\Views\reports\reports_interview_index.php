<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-clipboard-list mr-2"></i>
                        Interview Reports
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item active">Interview Reports</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Group Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-layer-group mr-2"></i>
                        <?= esc($positionGroup['group_name']) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('reports') ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i> Back to Reports
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Group Name:</strong></td>
                                    <td><?= esc($positionGroup['group_name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Description:</strong></td>
                                    <td><?= esc($positionGroup['description']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Total Positions:</strong></td>
                                    <td><?= count($positions) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Priority:</strong></td>
                                    <td><?= esc($positionGroup['priority']) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Positions for Interview Card -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Positions Available for Interview
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-success">
                            <?= count($positions) ?> <?= count($positions) > 1 ? 'Positions' : 'Position' ?>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> No Positions Available</h5>
                            <p>There are no positions marked for interview in this group. Please ensure positions are marked as "for interview" in the positions management.</p>
                            <a href="<?= base_url('positions') ?>" class="btn btn-primary">
                                <i class="fas fa-cog"></i> Manage Positions
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th width="10%">Position No.</th>
                                        <th width="25%">Designation</th>
                                        <th width="15%">Classification</th>
                                        <th width="10%">Award</th>
                                        <th width="8%" class="text-center">Applicants</th>
                                        <th width="8%" class="text-center">Questions</th>
                                        <th width="8%" class="text-center">Interviewers</th>
                                        <th width="16%" class="text-center">Interview Progress</th>
                                        <th width="10%" class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positions as $position): ?>
                                        <tr>
                                            <td class="align-middle"><?= esc($position['position_no']) ?></td>
                                            <td class="align-middle">
                                                <strong><?= esc($position['designation']) ?></strong>
                                            </td>
                                            <td class="align-middle"><?= esc($position['classification']) ?></td>
                                            <td class="align-middle"><?= esc($position['award']) ?></td>
                                            <td class="align-middle text-center">
                                                <span class="badge badge-info"><?= $position['shortlisted_count'] ?></span>
                                            </td>
                                            <td class="align-middle text-center">
                                                <span class="badge badge-primary"><?= $position['questions_count'] ?></span>
                                            </td>
                                            <td class="align-middle text-center">
                                                <span class="badge badge-secondary"><?= $position['interviewers_count'] ?></span>
                                            </td>
                                            <td class="align-middle">
                                                <?php if ($position['is_ready']): ?>
                                                    <div class="progress mb-1" style="height: 20px;">
                                                        <div class="progress-bar <?= $position['completion_percentage'] >= 100 ? 'bg-success' : 'bg-warning' ?>" 
                                                             role="progressbar" 
                                                             style="width: <?= min($position['completion_percentage'], 100) ?>%"
                                                             aria-valuenow="<?= $position['completion_percentage'] ?>" 
                                                             aria-valuemin="0" 
                                                             aria-valuemax="100">
                                                            <?= number_format($position['completion_percentage'], 0) ?>%
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?= $position['interview_data_count'] ?>/<?= $position['expected_entries'] ?> scores
                                                    </small>
                                                <?php else: ?>
                                                    <div class="progress mb-1" style="height: 20px;">
                                                        <div class="progress-bar bg-danger" 
                                                             role="progressbar" 
                                                             style="width: 100%">
                                                            Not Ready
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">Setup incomplete</small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="align-middle text-center">
                                                <?php if ($position['is_ready']): ?>
                                                    <a href="<?= base_url('interview-reports/view/' . $position['id']) ?>" 
                                                       class="btn btn-success btn-sm">
                                                        <i class="fas fa-chart-bar mr-1"></i> View Report
                                                    </a>
                                                <?php else: ?>
                                                    <button class="btn btn-secondary btn-sm" disabled>
                                                        <i class="fas fa-exclamation-triangle mr-1"></i> Not Ready
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary Statistics -->
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-briefcase"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Positions</span>
                                        <span class="info-box-number"><?= count($positions) ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-description">For Interview</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Ready Positions</span>
                                        <span class="info-box-number"><?= count(array_filter($positions, function($p) { return $p['is_ready']; })) ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: <?= count($positions) > 0 ? (count(array_filter($positions, function($p) { return $p['is_ready']; })) / count($positions)) * 100 : 0 ?>%"></div>
                                        </div>
                                        <span class="progress-description">Setup Complete</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Applicants</span>
                                        <span class="info-box-number"><?= array_sum(array_column($positions, 'shortlisted_count')) ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-description">Shortlisted</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box bg-primary">
                                    <span class="info-box-icon"><i class="fas fa-clipboard-list"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Interview Scores</span>
                                        <span class="info-box-number"><?= array_sum(array_column($positions, 'interview_data_count')) ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: <?= array_sum(array_column($positions, 'expected_entries')) > 0 ? (array_sum(array_column($positions, 'interview_data_count')) / array_sum(array_column($positions, 'expected_entries'))) * 100 : 0 ?>%"></div>
                                        </div>
                                        <span class="progress-description">Entered</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize DataTable
    $('table').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": true,
        "pageLength": 10,
        "order": [[ 0, "asc" ]], // Sort by position number
        "columnDefs": [
            { "orderable": false, "targets": [8] } // Disable sorting for action column
        ]
    });
});
</script>
<?= $this->endSection() ?>
