<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Positions in <?= esc($groupName) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('ratings') ?>">Ratings</a></li>
                        <li class="breadcrumb-item active">View Positions</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-briefcase mr-2"></i>
                                Positions in <?= esc($groupName) ?>
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr class="text-nowrap">
                                            <th>Position No.</th>
                                            <th>Designation</th>
                                            <th>Classification</th>
                                            <th>Qualifications</th>
                                            <th>Total Applicants</th>
                                            <th>Rating Status</th>
                                            <th width="15%">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($positions as $position): ?>
                                        <tr class="text-nowrap">
                                            <td class="align-middle">(<?= esc($position['position_no']) ?>)</td>
                                            <td class="align-middle"><?= esc($position['designation']) ?></td>
                                            <td class="align-middle"><?= esc($position['classification']) ?></td>
                                            <td class="align-middle"><?= esc($position['qualifications']) ?></td>
                                            <td class="align-middle text-center"><?= count($position['applicants'] ?? []) ?></td>
                                            <td class="align-middle">
                                                <?php
                                                $applicants = $position['applicants'] ?? [];
                                                $totalApplicants = count($applicants);
                                                $ratedApplicants = array_filter($applicants, function($applicant) {
                                                    return $applicant['rate_qualification'] != 0;
                                                });
                                                $ratedCount = count($ratedApplicants);
                                                ?>
                                                <?php if ($totalApplicants > 0): ?>
                                                    <?php if ($ratedCount == $totalApplicants): ?>
                                                        <span class="text-success"><i class="fas fa-check-circle"></i> All Rated (<?= $ratedCount ?>/<?= $totalApplicants ?>)</span>
                                                    <?php elseif ($ratedCount > 0): ?>
                                                        <span class="text-warning"><i class="fas fa-exclamation-circle"></i> Partially Rated (<?= $ratedCount ?>/<?= $totalApplicants ?>)</span>
                                                    <?php else: ?>
                                                        <span class="text-danger"><i class="fas fa-times-circle"></i> Not Rated (0/<?= $totalApplicants ?>)</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted"><i class="fas fa-info-circle"></i> No Applicants</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?= base_url("ratings/viewApplicants/{$position['id']}") ?>" class="btn btn-primary btn-sm">
                                                    View Applicants <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
