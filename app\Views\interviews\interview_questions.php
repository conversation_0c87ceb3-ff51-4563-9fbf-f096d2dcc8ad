<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-question-circle mr-2"></i>
                        Interview Questions
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/open/' . $position['id']) ?>">Open Interview</a></li>
                        <li class="breadcrumb-item active">Questions</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position: <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/open/' . $position['id']) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Interview
                        </a>
                    </div>
                </div>
            </div>

            <!-- Interview Questions Card -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list mr-2"></i>
                        Interview Questions
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/questions/create/' . $positionId) ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus mr-1"></i>
                            Add Question
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($questions)): ?>
                        <div class="alert alert-info m-3">
                            <h5><i class="icon fas fa-info-circle"></i> No Questions Added</h5>
                            <p>No interview questions have been added for this position yet. Click "Add Question" to create your first interview question.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="questionsTable">
                                <thead class="thead-light">
                                    <tr>
                                        <th width="8%">Question #</th>
                                        <th width="60%">Question Text</th>
                                        <th width="12%" class="text-center">Max Score</th>
                                        <th width="10%" class="text-center">Created</th>
                                        <th width="10%" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($questions as $question): ?>
                                        <tr>
                                            <td>
                                                <span class="badge badge-primary badge-lg">
                                                    Q<?= $question['question_no'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?= esc($question['question_text']) ?></strong>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge badge-warning">
                                                    <?= $question['set_score'] ?> pts
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($question['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('interviews/questions/edit/' . $question['id']) ?>" 
                                                       class="btn btn-warning btn-sm" data-toggle="tooltip" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            data-toggle="modal" 
                                                            data-target="#deleteModal<?= $question['id'] ?>"
                                                            data-toggle="tooltip" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal<?= $question['id'] ?>" tabindex="-1" role="dialog">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger">
                                                        <h5 class="modal-title text-white">
                                                            <i class="fas fa-exclamation-triangle mr-2"></i>
                                                            Confirm Delete
                                                        </h5>
                                                        <button type="button" class="close text-white" data-dismiss="modal">
                                                            <span>&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to delete this interview question?</p>
                                                        <div class="alert alert-light">
                                                            <strong>Question <?= $question['question_no'] ?>:</strong><br>
                                                            <?= esc(substr($question['question_text'], 0, 100)) ?><?= strlen($question['question_text']) > 100 ? '...' : '' ?>
                                                        </div>
                                                        <p class="text-danger"><small><strong>Warning:</strong> This action cannot be undone.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                        <form method="POST" action="<?= base_url('interviews/questions/delete/' . $question['id']) ?>" style="display: inline;">
                                                            <?= csrf_field() ?>
                                                            <button type="submit" class="btn btn-danger">
                                                                <i class="fas fa-trash mr-1"></i>
                                                                Delete Question
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Summary Card -->
            <div class="card card-info card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Questions Summary
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-question-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Questions</span>
                                    <span class="info-box-number"><?= count($questions) ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-star"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Points</span>
                                    <span class="info-box-number">
                                        <?php 
                                        $totalPoints = 0;
                                        foreach ($questions as $question) {
                                            $totalPoints += $question['set_score'];
                                        }
                                        echo $totalPoints;
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-calculator"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Average Points</span>
                                    <span class="info-box-number">
                                        <?= count($questions) > 0 ? round($totalPoints / count($questions), 1) : 0 ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#questionsTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": false,
        "order": [[ 0, "asc" ]], // Sort by question number
        "columnDefs": [
            { "orderable": false, "targets": [4] } // Disable sorting for actions column
        ]
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
<?= $this->endSection() ?>
