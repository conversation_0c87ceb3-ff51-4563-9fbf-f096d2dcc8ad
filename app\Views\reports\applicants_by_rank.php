<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Applicants by Rank - <?= esc($positionName) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("reports/viewPositions/{$groupId}") ?>"><?= esc($groupName) ?></a></li>
                        <li class="breadcrumb-item active">Applicants by Rank</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="text-center">
                                <img src="<?= imgcheck($org['org_logo']) ?>" alt="Organization Logo" class="img-fluid" style="align-items: center; max-width: 50px; display: block; margin: 0 auto;">
                            </div>
                            <h3 class="text-center"><?= session()->get('org_name') ?></h3>
                            <h4 class="text-center"><?= esc($groupName) ?></h4>
                            <h4 class="text-center"> <?= esc($positionNo) ?> - <?= esc($positionName) ?></h4>


                            <h3 class="card-title text-center">Ranked Applicants </h3>
                            
                                <div class="row d-flex justify-content-between no-print">
                                    <!-- Bulk actions column -->
                                    <!-- <div class="col-md-4">
                                        <div class="d-flex align-items-center">
                                            <div class="custom-control custom-checkbox mr-2">
                                                <input type="checkbox" class="custom-control-input" id="selectAll">
                                                <label class="custom-control-label" for="selectAll">Select All</label>
                                            </div>
                                        </div>
                                    </div> -->

                                    <!-- Bulk action dropdown with apply button -->
                                    <div class="col-md-3">
                                        <div class="input-group">
                                            <select id="bulkActionSelect" class="form-control">
                                                <option value="">Bulk Actions</option>
                                                <option value="shortlist">Shortlist Selected</option>
                                                <option value="eliminate">Eliminate Selected</option>
                                                <option value="withdraw">Withdraw Selected</option>
                                            </select>
                                            <div class="input-group-append">
                                                <button id="applyBulkAction" class="btn btn-primary">Apply</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Print options column -->
                                    <div class="col-md-3">
                                        <select id="noPrintDropdown" class="form-control" multiple>
                                            <!-- Options will be populated by JavaScript -->
                                        </select>
                                    </div>
                                    
                                    
                                    <div class="col-md-3">
                                        <select id="sortDropdown" class="form-control">
                                            <option value="">Sort by - All</option>
                                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                                <option value="<?= $i ?>"><?= $i ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select id="statusDropdown" class="form-control">
                                            <option value="All">Status - All</option>
                                            <option value="Shortlisted">Shortlisted</option>
                                            <option value="Eliminated">Eliminated</option>
                                            <option value="Withdrawn">Withdrawn</option>
                                        </select>
                                    </div>
                                
                                    
                                </div>

                                <script>
                                    document.addEventListener("DOMContentLoaded", function() {
                                        const tableHeaders = document.querySelectorAll("thead th");
                                        const dropdown = document.getElementById("noPrintDropdown");
                                        const tableRows = document.querySelectorAll("tbody tr");

                                        // Populate the dropdown with table headers
                                        tableHeaders.forEach((header, index) => {
                                            const option = document.createElement("option");
                                            option.value = index;
                                            option.textContent = header.textContent;
                                            option.selected = header.classList.contains("no-print");
                                            dropdown.appendChild(option);
                                        });

                                        // Function to toggle "no-print" class on entire column
                                        function toggleColumnPrint(columnIndex, shouldHide) {
                                            // Toggle class on the header
                                            if (shouldHide) {
                                                tableHeaders[columnIndex].classList.add("no-print");
                                            } else {
                                                tableHeaders[columnIndex].classList.remove("no-print");
                                            }

                                            // Toggle class on each cell in the column
                                            tableRows.forEach(row => {
                                                const cell = row.children[columnIndex];
                                                if (cell) {
                                                    if (shouldHide) {
                                                        cell.classList.add("no-print");
                                                    } else {
                                                        cell.classList.remove("no-print");
                                                    }
                                                }
                                            });
                                        }

                                        // Listen for changes in the dropdown to toggle the class on entire columns
                                        dropdown.addEventListener("change", function() {
                                            Array.from(dropdown.options).forEach((option, index) => {
                                                toggleColumnPrint(index, option.selected);
                                            });
                                        });
                                    });
                                </script>

                                <style>
                                    @media print {
                                        .no-print {
                                            display: none;
                                        }
                                    }

                                    /* Styles for eliminated applicants */
                                    .eliminated-row {
                                        background-color: rgba(255, 0, 0, 0.05) !important; /* Light red background */
                                        border: 2px solid rgba(255, 0, 0, 0.2) !important; /* Red border */
                                    }

                                    .eliminated-row td {
                                        border-color: rgba(255, 0, 0, 0.1) !important; /* Lighter red for cell borders */
                                    }

                                    /* Styles for shortlisted applicants */
                                    .shortlisted-row {
                                        background-color: rgba(0, 255, 0, 0.05) !important; /* Light green background */
                                        border: 2px solid rgba(0, 255, 0, 0.2) !important; /* Green border */
                                    }

                                    .shortlisted-row td {
                                        border-color: rgba(0, 255, 0, 0.1) !important; /* Lighter green for cell borders */
                                    }

                                    /* Styles for withdrawn applicants */
                                    .withdrawn-row {
                                        background-color: rgba(255, 255, 0, 0.05) !important; /* Light yellow background */
                                        border: 2px solid rgba(255, 255, 0, 0.2) !important; /* Yellow border */
                                    }

                                    .withdrawn-row td {
                                        border-color: rgba(255, 255, 0, 0.1) !important; /* Lighter yellow for cell borders */
                                    }
                                </style>

                                
                            

                            <script>
                                document.addEventListener("DOMContentLoaded", function() {
                                    const sortDropdown = document.getElementById("sortDropdown");
                                    const statusDropdown = document.getElementById("statusDropdown");
                                    const tableBody = document.querySelector("table tbody");
                                    const allRows = Array.from(tableBody.rows);

                                    // Function to get status cell content
                                    function getStatusFromRow(row) {
                                        // Get the status cell (index 6 since we added checkbox column)
                                        return row.querySelector('td:nth-child(7)').textContent.trim();
                                    }

                                    // Function to display rows based on filters
                                    function filterAndDisplayRows() {
                                        const selectedStatus = statusDropdown.value;
                                        const selectedCount = parseInt(sortDropdown.value, 10) || allRows.length;
                                        let displayedCount = 0;

                                        // First hide all rows
                                        allRows.forEach(row => row.style.display = 'none');

                                        // Filter and show rows
                                        allRows.forEach(row => {
                                            const status = getStatusFromRow(row);
                                            
                                            // Check if row matches status filter
                                            const matchesStatus = selectedStatus === 'All' || status === selectedStatus;

                                            // Show row if it matches filters and we haven't hit our limit
                                            if (matchesStatus && displayedCount < selectedCount) {
                                                row.style.display = '';
                                                displayedCount++;
                                            }
                                        });

                                        // Update the select all checkbox
                                        updateSelectAllCheckbox();
                                    }

                                    // Function to update "Select All" checkbox state
                                    function updateSelectAllCheckbox() {
                                        const headerCheckbox = document.getElementById('headerCheckbox');
                                        const visibleCheckboxes = Array.from(document.querySelectorAll('.applicant-checkbox'))
                                            .filter(checkbox => checkbox.closest('tr').style.display !== 'none');
                                        
                                        if (visibleCheckboxes.length === 0) {
                                            headerCheckbox.checked = false;
                                            headerCheckbox.indeterminate = false;
                                        } else {
                                            const allChecked = visibleCheckboxes.every(checkbox => checkbox.checked);
                                            const someChecked = visibleCheckboxes.some(checkbox => checkbox.checked);
                                            
                                            headerCheckbox.checked = allChecked;
                                            headerCheckbox.indeterminate = someChecked && !allChecked;
                                        }
                                    }

                                    // Event listener for status dropdown
                                    statusDropdown.addEventListener('change', filterAndDisplayRows);

                                    // Event listener for sort dropdown
                                    sortDropdown.addEventListener('change', filterAndDisplayRows);

                                    // Event listener for header checkbox
                                    document.getElementById('headerCheckbox').addEventListener('change', function(e) {
                                        const visibleCheckboxes = Array.from(document.querySelectorAll('.applicant-checkbox'))
                                            .filter(checkbox => checkbox.closest('tr').style.display !== 'none');
                                        
                                        visibleCheckboxes.forEach(checkbox => {
                                            checkbox.checked = e.target.checked;
                                        });
                                    });

                                    // Event listeners for individual checkboxes
                                    document.querySelectorAll('.applicant-checkbox').forEach(checkbox => {
                                        checkbox.addEventListener('change', updateSelectAllCheckbox);
                                    });

                                    // Initial display
                                    filterAndDisplayRows();
                                });
                            </script>

                        </div>
                        <div class="card-body">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th class="no-print">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="headerCheckbox">
                                                <label class="custom-control-label" for="headerCheckbox"></label>
                                            </div>
                                        </th>
                                        <th>Rank</th>
                                        <th>Name</th>
                                        <th>Position No.</th>
                                        <th>Position</th>
                                        <th>Total Rating</th>
                                        <th>Status</th>
                                        <th>Reason</th>
                                        <th class="no-print">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($rankedApplicants as $applicant): ?>
                                        <tr class="<?php 
                                            if ($applicant['application_status'] === 'Eliminated') {
                                                echo 'eliminated-row';
                                            } elseif ($applicant['application_status'] === 'Shortlisted') {
                                                echo 'shortlisted-row';
                                            } elseif ($applicant['application_status'] === 'Withdrawn') {
                                                echo 'withdrawn-row';
                                            }
                                        ?>">
                                            <td class="no-print">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" class="custom-control-input applicant-checkbox" 
                                                           id="applicant<?= $applicant['id'] ?>" 
                                                           value="<?= $applicant['id'] ?>">
                                                    <label class="custom-control-label" for="applicant<?= $applicant['id'] ?>"></label>
                                                </div>
                                            </td>
                                            <td><?= esc($applicant['overall_rank']) ?></td>
                                            <td><?= esc($applicant['name']) ?></td>
                                            <td><?= esc($applicant['position_no']) ?></td>
                                            <td><?= esc($applicant['position']) ?></td>
                                            <td><?= esc($applicant['rate_total']) ?> / 61</td>
                                            <td id="statusCell<?= $applicant['id'] ?>"><?= esc($applicant['application_status']) ?></td>
                                            <td><?= esc($applicant['app_status_reason']) ?></td>
                                            <td class="no-print">
                                                <div class="btn-group">
                                                    <!-- Shortlist button -->
                                                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#shortlistModal<?= $applicant['id'] ?>">Shortlist</button>

                                                    <!-- Eliminate button -->
                                                    <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#eliminateModal<?= $applicant['id'] ?>">Eliminate</button>

                                                    <!-- Withdraw button -->
                                                    <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#withdrawModal<?= $applicant['id'] ?>">Withdraw</button>
                                                </div>

                                                <!-- Shortlist modal -->
                                                <div class="modal fade" id="shortlistModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="shortlistModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="shortlistModalLabel<?= $applicant['id'] ?>">Shortlist Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="form-group">
                                                                    <label for="shortlistReason<?= $applicant['id'] ?>">Reason for Shortlisting</label>
                                                                    <textarea class="form-control" id="shortlistReason<?= $applicant['id'] ?>" required></textarea>
                                                                </div>
                                                                <button type="button" class="btn btn-primary" onclick="shortlistApplicant(<?= $applicant['id'] ?>)">Shortlist</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Eliminate modal -->
                                                <div class="modal fade" id="eliminateModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="eliminateModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="eliminateModalLabel<?= $applicant['id'] ?>">Eliminate Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="form-group">
                                                                    <label for="eliminateReason<?= $applicant['id'] ?>">Reason for Elimination</label>
                                                                    <textarea class="form-control" id="eliminateReason<?= $applicant['id'] ?>" required></textarea>
                                                                </div>
                                                                <button type="button" class="btn btn-danger" onclick="eliminateApplicant(<?= $applicant['id'] ?>)">Eliminate</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Withdraw modal -->
                                                <div class="modal fade" id="withdrawModal<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="withdrawModalLabel<?= $applicant['id'] ?>" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="withdrawModalLabel<?= $applicant['id'] ?>">Withdraw Applicant</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="form-group">
                                                                    <label for="withdrawReason<?= $applicant['id'] ?>">Reason for Withdrawal</label>
                                                                    <textarea class="form-control" id="withdrawReason<?= $applicant['id'] ?>" required></textarea>
                                                                </div>
                                                                <button type="button" class="btn btn-warning" onclick="withdrawApplicant(<?= $applicant['id'] ?>)">Withdraw</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Add Bulk Action Modals -->
<!-- Bulk Shortlist Modal -->
<div class="modal fade" id="bulkShortlistModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Shortlist Selected Applicants</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkShortlistForm">
                    <div class="form-group">
                        <label for="bulkShortlistReason">Reason for Shortlisting</label>
                        <textarea class="form-control" id="bulkShortlistReason" name="shortlistReason" required></textarea>
                    </div>
                    <input type="hidden" id="bulkShortlistIds" name="applicantIds">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmBulkShortlist">Shortlist</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Eliminate Modal -->
<div class="modal fade" id="bulkEliminateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Eliminate Selected Applicants</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkEliminateForm">
                    <div class="form-group">
                        <label for="bulkEliminateReason">Reason for Elimination</label>
                        <textarea class="form-control" id="bulkEliminateReason" name="eliminateReason" required></textarea>
                    </div>
                    <input type="hidden" id="bulkEliminateIds" name="applicantIds">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmBulkEliminate">Eliminate</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Withdraw Modal -->
<div class="modal fade" id="bulkWithdrawModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Withdraw Selected Applicants</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkWithdrawForm">
                    <div class="form-group">
                        <label for="bulkWithdrawReason">Reason for Withdrawal</label>
                        <textarea class="form-control" id="bulkWithdrawReason" name="withdrawReason" required></textarea>
                    </div>
                    <input type="hidden" id="bulkWithdrawIds" name="applicantIds">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmBulkWithdraw">Withdraw</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

<script>
// Initialize Toastr
toastr.options = {
    "closeButton": true,
    "progressBar": true,
    "positionClass": "toast-top-right",
    "timeOut": "3000"
};

// Function to update applicant status in the UI
function updateApplicantStatus(applicantId, status) {
    const statusCell = document.getElementById(`statusCell${applicantId}`);
    if (statusCell) {
        statusCell.textContent = status;
    }
}

// Function to handle shortlist action
function shortlistApplicant(applicantId) {
    const reason = document.getElementById(`shortlistReason${applicantId}`).value;
    
    if (!reason) {
        toastr.error('Please provide a reason');
        return;
    }

    $.ajax({
        url: '<?= base_url('applicants/shortlist') ?>/' + applicantId,
        method: 'POST',
        data: {
            shortlistReason: reason,
            <?= csrf_token() ?>: '<?= csrf_hash() ?>'
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message || 'Applicant has been shortlisted successfully');
                $(`#shortlistModal${applicantId}`).modal('hide');
                updateApplicantStatus(applicantId, 'Shortlisted');
                setTimeout(() => { location.reload(); }, 1500);
            } else {
                toastr.error(response.error || 'Failed to shortlist applicant');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            toastr.error(xhr.responseJSON?.error || 'An error occurred while shortlisting the applicant');
        }
    });
}

// Function to handle eliminate action
function eliminateApplicant(applicantId) {
    const reason = document.getElementById(`eliminateReason${applicantId}`).value;
    
    if (!reason) {
        toastr.error('Please provide a reason');
        return;
    }

    $.ajax({
        url: '<?= base_url('applicants/eliminate') ?>/' + applicantId,
        method: 'POST',
        data: {
            eliminateReason: reason,
            <?= csrf_token() ?>: '<?= csrf_hash() ?>'
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message || 'Applicant has been eliminated successfully');
                $(`#eliminateModal${applicantId}`).modal('hide');
                updateApplicantStatus(applicantId, 'Eliminated');
                setTimeout(() => { location.reload(); }, 1500);
            } else {
                toastr.error(response.error || 'Failed to eliminate applicant');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            toastr.error(xhr.responseJSON?.error || 'An error occurred while eliminating the applicant');
        }
    });
}

// Function to handle withdraw action
function withdrawApplicant(applicantId) {
    const reason = document.getElementById(`withdrawReason${applicantId}`).value;
    
    if (!reason) {
        toastr.error('Please provide a reason');
        return;
    }

    $.ajax({
        url: '<?= base_url('applicants/withdraw') ?>/' + applicantId,
        method: 'POST',
        data: {
            withdrawReason: reason,
            <?= csrf_token() ?>: '<?= csrf_hash() ?>'
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message || 'Applicant has been withdrawn successfully');
                $(`#withdrawModal${applicantId}`).modal('hide');
                updateApplicantStatus(applicantId, 'Withdrawn');
                setTimeout(() => { location.reload(); }, 1500);
            } else {
                toastr.error(response.error || 'Failed to withdraw applicant');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            toastr.error(xhr.responseJSON?.error || 'An error occurred while withdrawing the applicant');
        }
    });
}

$(document).ready(function() {
    // Handle "Select All" checkbox
    $('#headerCheckbox').change(function() {
        $('.applicant-checkbox').prop('checked', $(this).is(':checked'));
    });

    // Handle bulk action button click
    $('#applyBulkAction').click(function() {
        const selectedAction = $('#bulkActionSelect').val();
        const selectedApplicants = $('.applicant-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (!selectedAction) {
            alert('Please select an action');
            return;
        }

        if (selectedApplicants.length === 0) {
            alert('Please select at least one applicant');
            return;
        }

        // Show appropriate modal based on selected action
        switch(selectedAction) {
            case 'shortlist':
                $('#bulkShortlistIds').val(selectedApplicants.join(','));
                $('#bulkShortlistModal').modal('show');
                break;
            case 'eliminate':
                $('#bulkEliminateIds').val(selectedApplicants.join(','));
                $('#bulkEliminateModal').modal('show');
                break;
            case 'withdraw':
                $('#bulkWithdrawIds').val(selectedApplicants.join(','));
                $('#bulkWithdrawModal').modal('show');
                break;
        }
    });

    // Handle bulk shortlist confirmation
    $('#confirmBulkShortlist').click(function() {
        const ids = $('#bulkShortlistIds').val();
        const reason = $('#bulkShortlistReason').val();
        
        if (!reason) {
            toastr.error('Please provide a reason');
            return;
        }

        $.ajax({
            url: '<?= base_url('reports/bulk_shortlist') ?>',
            method: 'POST',
            data: {
                applicantIds: ids,
                shortlistReason: reason,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message || 'Applicants have been shortlisted successfully');
                    setTimeout(() => { location.reload(); }, 1500);
                } else {
                    toastr.error(response.error || 'Failed to shortlist applicants');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                toastr.error(xhr.responseJSON?.error || 'An error occurred while processing the request');
            }
        });
    });

    // Handle bulk eliminate confirmation
    $('#confirmBulkEliminate').click(function() {
        const ids = $('#bulkEliminateIds').val();
        const reason = $('#bulkEliminateReason').val();
        
        if (!reason) {
            toastr.error('Please provide a reason');
            return;
        }

        $.ajax({
            url: '<?= base_url('reports/bulk_eliminate') ?>',
            method: 'POST',
            data: {
                applicantIds: ids,
                eliminateReason: reason,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message || 'Applicants have been eliminated successfully');
                    setTimeout(() => { location.reload(); }, 1500);
                } else {
                    toastr.error(response.error || 'Failed to eliminate applicants');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                toastr.error(xhr.responseJSON?.error || 'An error occurred while processing the request');
            }
        });
    });

    // Handle bulk withdraw confirmation
    $('#confirmBulkWithdraw').click(function() {
        const ids = $('#bulkWithdrawIds').val();
        const reason = $('#bulkWithdrawReason').val();
        
        if (!reason) {
            toastr.error('Please provide a reason');
            return;
        }

        $.ajax({
            url: '<?= base_url('reports/bulk_withdraw') ?>',
            method: 'POST',
            data: {
                applicantIds: ids,
                withdrawReason: reason,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message || 'Applicants have been withdrawn successfully');
                    setTimeout(() => { location.reload(); }, 1500);
                } else {
                    toastr.error(response.error || 'Failed to withdraw applicants');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                toastr.error(xhr.responseJSON?.error || 'An error occurred while processing the request');
            }
        });
    });
});
</script>
<?= $this->endSection() ?>