<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

<body>
    <div class="container-fluid p-2 ">
        <div class="row">
            <div class="col-md-8">
                <h5 class=" float-left">Manage District [<?= $district['district_name'] ?>]</h5>
            </div>
            <div class="col-md-4">
                <nav class="breadcrumb float-right">
                    <a class="breadcrumb-item" href="<?= base_url() ?>dakoii/view_manage_province/<?= $province['ucode'] ?>" class=""> <span class=" float-right"><i class="fas fa-arrow-left" aria-hidden="true"></i> Back</span> </a>
                    <a class="breadcrumb-item " href="#">Country</a>
                    <a class="breadcrumb-item " href="#">Province</a>
                    <a class="breadcrumb-item active" href="#">District</a>
                </nav>
            </div>
        </div>
        <div class="row">
            <div class="col-md-7">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="btn-group" role="group" aria-label="button group">
                                    <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#upload_flag"><i class="fa fa-upload" aria-hidden="true"></i> Flag</button>
                                    <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#map_centre"> <i class="fas fa-bullseye"></i> Map Centre</button>
                                </div>

                                <!-- Modal -->
                                <div class="modal fade" id="map_centre" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header bg-primary text-dark">
                                                <h5 class="modal-title"> <i class="fa fa-map-marker-alt" aria-hidden="true"></i> Set Map Center</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <?= form_open('update_map_center') ?>
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label for="">Map Center GPS</label>
                                                    <input type="text" class="form-control" name="map_center" id="" aria-describedby="helpId" placeholder="latitude,-longitude" value="<?= $district['map_center'] ?>">
                                                    <small id="helpId" class="form-text text-muted">Set Center of the Map</small>
                                                </div>
                                                <div class="form-group">
                                                    <label for="">Map Zoom</label>
                                                    <input type="number" class="form-control" name="map_zoom" id="" aria-describedby="helpId" placeholder="Zoom Number" value="<?= $district['map_zoom'] ?>">
                                                    <small id="helpId" class="form-text text-muted">Set Zoom Level of the Map</small>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="hidden" name="id" value="<?= $district['id'] ?>">
                                                <input type="hidden" name="gov_level" value="district">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                <button type="submit" class="btn btn-primary"><i class="fa fa-paper-plane" aria-hidden="true"></i> Set Map Center</button>
                                            </div>
                                            <?= form_close() ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Modal -->
                                <div class="modal fade" id="upload_flag" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header bg-primary text-dark">
                                                <h5 class="modal-title"> <i class="fas fa-upload" aria-hidden="true"></i> Upload Flag</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <?= form_open_multipart('upload_flag') ?>
                                            <div class="modal-body">
                                                <h5>Flag of <?= $district['district_name'] ?></h5>
                                                <div class="form-group">
                                                    <label for="flagInput">Flag Image</label>
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" name="flag" id="flagInput" accept="image/*">
                                                        <label class="custom-file-label" for="flagInput">Choose file</label>
                                                    </div>
                                                </div>

                                                <script>
                                                    // Update the label of the file input with the selected file name
                                                    $(document).ready(function() {
                                                        $('.custom-file-input').on('change', function(event) {
                                                            var inputFile = event.currentTarget;
                                                            $(inputFile).parent().find('.custom-file-label').html(inputFile.files[0].name);
                                                        });
                                                    });
                                                </script>

                                            </div>
                                            <div class="modal-footer">
                                                <input type="hidden" name="id" value="<?= $district['id'] ?>">
                                                <input type="hidden" name="gov_level" value="district">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                <button type="submit" class="btn btn-primary"> <i class="fa fa-upload" aria-hidden="true"></i> Upload</button>
                                            </div>
                                            <?= form_close() ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <img src="<?= imgcheck($district['flag']) ?>" class="img-fluid ${3|rounded-top,rounded-right,rounded-bottom,rounded-left,rounded-circle,|}" alt="">

                                        <h2>[<?= $district['district_code'] ?>] <?= $district['district_name'] ?></h2>
                                        <br>
                                        <p> <b>GPS: </b> <?= $district['map_center'] ?></p>
                                        <p> <b>Zoom: </b> <?= $district['map_zoom'] ?></p>
                                    </div>
                                    <div class="col-md-8">
                                        <style>
                                            #map {
                                                height: 500px;
                                                /* Adjust the height as needed */
                                            }
                                        </style>
                                        <!-- Map container -->
                                        <div id="map"></div>

                                        <script>
                                            // Initialize the map
                                            var map = L.map('map').setView([<?= $district['map_center'] ?>], <?= $district['map_zoom'] ?>); // Replace with your desired coordinates and zoom level

                                            // Add OpenStreetMap tiles
                                            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                            }).addTo(map);

                                            // Add a marker at the specified coordinates
                                            L.marker([<?= $district['map_center'] ?>]).addTo(map)
                                                .bindPopup('Center of the map')
                                                .openPopup();
                                        </script>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <span class=" float-left">District Users</span>
                                <!-- Button trigger modal -->
                                <button type="button" class="btn btn-primary btn-sm float-right" data-toggle="modal" data-target="#create_users">
                                    <i class="fa fa-user-plus" aria-hidden="true"></i> District Users
                                </button>

                                <!-- Modal -->
                                <div class="modal fade" id="create_users" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header bg-primary text-light">
                                                <h5 class="modal-title"> <i class="fa fa-user-plus" aria-hidden="true"></i> Create District User</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <?= form_open_multipart('create_user') ?>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="form-group col-md-6">
                                                        <label for="my-input ">First Name</label>
                                                        <input id="my-input" class="form-control" type="text" name="fname">
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="my-input ">Last Name</label>
                                                        <input id="my-input" class="form-control" type="text" name="lname">
                                                    </div>
                                                    <div class="form-group col-md-2">
                                                        <label for="my-input">User Role</label>
                                                        <select name="user_role" id="" class="form-control">
                                                            <option value="admin">Admin</option>
                                                            <option value="editor">Editor</option>
                                                            <option value="viewer">Viewer</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-md-2">
                                                        <label for="my-input">Previledge</label>
                                                        <select name="user_previledge" id="" class="form-control">
                                                            <option value="default">Default</option>
                                                            <option value="super">Super</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-md-4">
                                                        <label for="my-input">Username</label>
                                                        <input id="my-input" class="form-control" type="text" name="username">
                                                    </div>
                                                    <div class="form-group col-md-4">
                                                        <label for="my-input">Password</label>
                                                        <input id="my-input" class="form-control" type="password" name="password">
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="my-input">Phone</label>
                                                        <input id="my-input" class="form-control" type="text" name="phone">
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="my-input">Email</label>
                                                        <input id="my-input" class="form-control" type="text" name="email">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="hidden" name="user_type" value="gov">
                                                <input type="hidden" name="access_level" value="district">
                                                <input type="hidden" name="group_id" value="<?= $district['id'] ?>">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                <button type="submit" class="btn btn-primary"> <i class="fa fa-paper-plane" aria-hidden="true"></i> Create User</button>
                                            </div>
                                            <?= form_close(); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <table class="table bg-light">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Username</th>
                                            <th>Name</th>
                                            <th>Role</th>
                                            <th>Ph#</th>
                                            <th>Email</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $x = 1;
                                        foreach ($users as $user) : ?>
                                            <tr>
                                                <td scope="row"><?= $x++ ?></td>
                                                <td><?= $user['username'] ?></td>
                                                <td><?= $user['fname'] ?> <?= $user['lname'] ?></td>
                                                <td><?= $user['role'] ?> <span class="badge badge-info"><?= $user['previledge'] ?></span> </td>
                                                <td><?= $user['phone'] ?> </td>
                                                <td><?= $user['email'] ?> </td>
                                                <td>
                                                    <div class="btn-group float-right" role="group" aria-label="Button group">
                                                        <!-- Button trigger modal -->
                                                        <button type="button" class="btn btn-sm btn-secondary" data-toggle="modal" data-target="#edit<?= $user['id'] ?>">edit</button>
                                                        <button type="button" class="btn btn-sm btn-warning" data-toggle="modal" data-target="#pwd<?= $user['id'] ?>">pwd</button>
                                                    </div>


                                                    <!-- Modal -->
                                                    <div class="modal fade" id="edit<?= $user['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                                        <div class="modal-dialog modal-lg" role="document">
                                                            <div class="modal-content">
                                                                <div class="modal-header bg-secondary text-dark">
                                                                    <h5 class="modal-title"> <i class="fa fa-user-plus" aria-hidden="true"></i> Edit Province Users</h5>
                                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <?= form_open_multipart('update_user') ?>
                                                                <div class="modal-body">
                                                                    <div class="row">
                                                                        <div class="form-group col-md-12">
                                                                            <label for="my-input">Username: <b><?= $user['username'] ?></b></label>
                                                                        </div>
                                                                        <div class="form-group col-md-4">
                                                                            <label for="my-input ">First Name</label>
                                                                            <input id="my-input" class="form-control" type="text" name="fname" value="<?= $user['fname'] ?>">
                                                                        </div>
                                                                        <div class="form-group col-md-4">
                                                                            <label for="my-input ">Last Name</label>
                                                                            <input id="my-input" class="form-control" type="text" name="lname" value="<?= $user['lname'] ?>">
                                                                        </div>
                                                                        <div class="form-group col-md-2">
                                                                            <label for="my-input">User Role</label>
                                                                            <select name="user_role" id="" class="form-control">
                                                                                <option value="<?= $user['role'] ?>"><?= ucfirst($user['role']) ?></option>
                                                                                <option value="admin">Admin</option>
                                                                                <option value="editor">Editor</option>
                                                                                <option value="viewer">Viewer</option>
                                                                            </select>
                                                                        </div>
                                                                        <div class="form-group col-md-2">
                                                                            <label for="my-input">Previledge</label>
                                                                            <select name="user_previledge" id="" class="form-control">
                                                                                <option value="<?= $user['previledge'] ?>"><?= ucfirst($user['previledge']) ?></option>
                                                                                <option value="default">Default</option>
                                                                                <option value="super">Super</option>
                                                                            </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                            <label for="my-input">Phone</label>
                                                                            <input id="my-input" class="form-control" type="text" name="phone" value="<?= $user['phone'] ?>">
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                            <label for="my-input">Email</label>
                                                                            <input id="my-input" class="form-control" type="text" name="email" value="<?= $user['email'] ?>">
                                                                        </div>
                                                                        <div class="form-group col-md-2">
                                                                            <label for="my-input">Status: <b><?= $user['is_active'] ?></b></label>
                                                                            <select name="status" id="" class="form-control">
                                                                                <option value="<?= $user['is_active'] ?>"><?= $user['is_active'] ?></option>
                                                                                <option value="1">Active [1]</option>
                                                                                <option value="0">Inactive [0]</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <input type="hidden" name="id" value="<?= $user['id'] ?>">
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                                    <button type="submit" class="btn btn-primary"> <i class="fa fa-paper-plane" aria-hidden="true"></i> Save Changes</button>
                                                                </div>
                                                                <?= form_close(); ?>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Modal -->
                                                    <div class="modal fade" id="pwd<?= $user['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                                        <div class="modal-dialog" role="document">
                                                            <div class="modal-content">
                                                                <div class="modal-header bg-warning text-dark">
                                                                    <h5 class="modal-title"> <i class="fa fa-lock" aria-hidden="true"></i> Reset Password</h5>
                                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <?= form_open('user_password_reset'); ?>
                                                                <div class="modal-footer">
                                                                    <p>Password will be reset to default password: <b>1234</b></p>
                                                                    <input type="hidden" name="password" value="1234">
                                                                    <input id="my-input" type="hidden" name="id" value="<?= $user['id'] ?>">
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                                    <button type="submit" class="btn btn-warning">Reset</button>
                                                                </div>
                                                                <?= form_close(); ?>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>


                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-md-5">
                <div class="card">
                    <div class="card-header">
                        List of LLGs

                        <!-- Button trigger modal -->
                        <button type="button" class="btn btn-primary btn-sm float-right" data-toggle="modal" data-target="#create_llg">
                            <i class="fa fa-plus-circle" aria-hidden="true"></i> New LLG
                        </button>

                        <!-- Modal -->
                        <div class="modal fade" id="create_llg" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header bg-primary text-dark">
                                        <h5 class="modal-title"> <i class="fa fa-plus-circle" aria-hidden="true"></i> New LLG</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <?= form_open('dakoii/create_llg'); ?>
                                    <div class="modal-body">
                                        <div class="form-group">
                                            <label for="my-input">LLG Code</label>
                                            <input id="my-input" class="form-control" type="text" name="llg_code" placeholder="LLG Code">
                                        </div>
                                        <div class="form-group">
                                            <label for="my-input">LLG Name</label>
                                            <input id="my-input" class="form-control" type="text" name="llg_name" placeholder="LLG Name">
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <input type="hidden" name="country_id" value="<?= $country['id'] ?>">
                                        <input type="hidden" name="province_id" value="<?= $province['id'] ?>">
                                        <input type="hidden" name="district_id" value="<?= $district['id'] ?>">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                        <button type="submit" class="btn btn-primary"> <i class="fa fa-paper-plane" aria-hidden="true"></i> Create</button>
                                    </div>
                                    <?= form_close(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <table class="table bg-light text-dark">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $x = 1;
                                foreach ($llgs as $llg) : ?>
                                    <tr>
                                        <td scope="row"><?= $x++ ?></td>
                                        <td><?= $llg['llg_code'] ?></td>
                                        <td><?= $llg['llg_name'] ?></td>
                                        <td>
                                            <div class="btn-group float-right" role="group" aria-label="button group">
                                                <a href="<?= base_url() ?>dakoii/view_manage_llgs/<?= $llg['ucode'] ?>" class="btn btn-sm btn-primary">Open</a>
                                                <button type="button" class="btn btn-sm btn-secondary" data-toggle="modal" data-target="#edit<?= $llg['id'] ?>"> Edit</button>

                                                <!-- Modal -->
                                                <div class="modal fade" id="edit<?= $llg['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header bg-secondary text-dark">
                                                                <h5 class="modal-title"> <i class="fas fa-edit"></i> Edit LLG</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <?= form_open('dakoii/edit_llg'); ?>
                                                            <div class="modal-body">
                                                                <div class="form-group">
                                                                    <label for="my-input">LL Code</label>
                                                                    <input id="my-input" class="form-control" type="text" name="llg_code" placeholder="LLG Code" value="<?= $llg['llg_code'] ?>">
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="my-input">LL Name</label>
                                                                    <input id="my-input" class="form-control" type="text" name="llg_name" placeholder="LLG Name" value="<?= $llg['llg_name'] ?>">
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <input type="hidden" name="id" value="<?= $llg['id'] ?>">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                                <button type="submit" class="btn btn-primary"> <i class="fas fa-save "></i> Save Changes</button>
                                                            </div>
                                                            <?= form_close(); ?>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
</body>



</html>
<?= $this->endSection() ?>