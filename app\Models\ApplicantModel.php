<?php

namespace App\Models;

use CodeIgniter\Model;

class ApplicantModel extends Model
{
    protected $table      = 'applicants';        // Name of the table
    protected $primaryKey = 'id';                // Primary key of the table

    protected $useAutoIncrement = true;          // Primary key is auto-incremented

    // Fields that are allowed to be inserted/updated
    protected $allowedFields = [
        'org_id',
        'position_id',
        'position_group_id',
        'name',
        'sex',
        'age',
        'place_origin',
        'contact_details', //new field
        'nid_number', //new field
        'current_employer',
        'current_position',
        'address_location',
        'qualification_text',
        'other_trainings',
        'knowledge',
        'skills_competencies',
        'job_experiences',
        'publications', 
        'awards', 
        'referees', 
        'comments',
        'rate_age',
        'max_rate_age',
        'rate_qualification',
        'max_rate_qualification',
        'rate_experience',
        'rate_private_non_relevant',
        'max_rate_private_non_relevant',
        'rate_private_relevant',
        'max_rate_private_relevant',
        'rate_public_non_relevant',
        'max_rate_public_non_relevant',
        'rate_public_relevant',
        'max_rate_public_relevant',
        'max_rate_experience',
        'rate_trainings',
        'max_rate_trainings',
        'rate_skills_competencies',
        'max_rate_skills_competencies',
        'rate_knowledge',
        'max_rate_knowledge',
        'rate_public_service',
        'max_rate_public_service',
        'rate_capability',
        'max_rate_capability',
        'rate_total',
        'max_rate_total',
        'remarks',
        'application_status',
        'app_status_reason',
        'pre_select_status',
        'interview_notices',
        'updated_by'
    ];

    // Automatically handle the timestamps for created_at and updated_at
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Optional: Soft deletes can be enabled if needed
    protected $useSoftDeletes = false;

    // Optional: Default values for specific fields (like is_active)
    protected $defaultValues = [
        'is_active' => 1
    ];
}
