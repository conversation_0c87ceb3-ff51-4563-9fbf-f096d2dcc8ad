<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Applicant Profiles</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="invoice p-3 mb-3">
                        <div class="row">
                            <div class="col-12 text-center">
                                <img src="<?= base_url('assets/img/papua_new_guinea_coat_of_arms.png') ?>" alt="Papua New Guinea Coat of Arms" style="width: 100px;">
                                <h4 class="mt-3">PUBLIC SERVICE OF PAPUA NEW GUINEA</h4>
                                <h5>SHORT LIST APPLICANT PROFILE</h5>
                                <p class="float-right">FORM RS 3.7</p>
                            </div>
                        </div>
                        <div class="row invoice-info mt-4">
                            <div class="col-sm-6 invoice-col">
                                <b>Advertisement No:</b> <span><?= esc($org['advertisement_no']) ?></span><br>
                                <b>Advertisement Date:</b> <span><?= esc($org['advertisement_date']) ?></span><br>
                                <b>Mode of Advert:</b> <span><?= esc($org['mode_of_advert']) ?></span>
                                <br>
                                <b>Total No. Of Applicants:</b> <span><?= count($applicants) ?></span>
                            
                            </div>
                            <div class="col-sm-6 invoice-col">
                                <b>Position No:</b> <span><?= esc($position['position_no']) ?></span><br>
                                <b>Designation:</b> <span><?= esc($position['designation']) ?></span><br>
                                <b>Classification:</b> <span><?= esc($position['classification']) ?></span>
                            </div>
                            
                        </div>

                        <?php
                        $totalApplicants = count($applicants);
                        $applicantsPerPage = 3;
                        $totalPages = ceil($totalApplicants / $applicantsPerPage);
                        $currentPage = isset($_GET['page']) ? max(1, min($totalPages, intval($_GET['page']))) : 1;
                        $startIndex = ($currentPage - 1) * $applicantsPerPage;
                        $currentApplicants = array_slice($applicants, $startIndex, $applicantsPerPage);
                        ?>

                        <div class="row mt-4">
                            <div class="col-12 table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                        <tr class="bg-gray">
                                            <th>POSITION SPECIFICATION</th>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <th>PERSON SPECIFICATION<br>Applicant <?= $startIndex + $i + 1 ?></th>
                                            <?php endfor; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td></td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td>
                                                 <b>Name:</b>   <?= esc($currentApplicants[$i]['name']) ?><br>
                                                 <b>Sex:</b>    <?= esc($currentApplicants[$i]['sex']) ?><br>
                                                 <b>Age:</b>    <?= esc($currentApplicants[$i]['age']) ?><br>
                                                 <b>Current Position:</b> <?= esc($currentApplicants[$i]['current_position']) ?><br>
                                                 <b>Address/Location:</b> <?= esc($currentApplicants[$i]['address_location']) ?><br>
                                                 
                                                </td>
                                            <?php endfor; ?>
                                        </tr>
                                        
                                        <tr class="bg-gray">
                                            <td>Qualifications (abstract from 
                                            JD):</td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td></td>
                                            <?php endfor; ?>
                                        </tr>
                                        
                                        <tr>
                                            <td><?= esc($position['qualifications']) ?></td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td><?= esc($currentApplicants[$i]['qualification_text']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr class="bg-gray">
                                            <td>Other training/courses 
                                            attended</td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td></td>
                                            <?php endfor; ?>
                                        </tr>
                                        
                                        <tr>
                                            <td> </td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td><?= esc($currentApplicants[$i]['other_trainings']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr class="bg-gray">
                                            <td>Knowledge:</td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td><?= esc($position['knowledge']) ?></td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td><?= esc($currentApplicants[$i]['knowledge']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr class="bg-gray">
                                            <td>Skills/Competencies</td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td><?= esc($position['skills_competencies']) ?></td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td><?= esc($currentApplicants[$i]['skills_competencies']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr class="bg-gray">
                                            <td>Related Job Experience:</td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td><?= esc($position['job_experiences']) ?></td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td><?= esc($currentApplicants[$i]['job_experiences']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr class="bg-gray">
                                            <td>Comments</td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <?php for ($i = 0; $i < count($currentApplicants); $i++): ?>
                                                <td><?= esc($currentApplicants[$i]['comments']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12 table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Ranking out of 10</th>
                                            <?php for ($i = 0; $i < min(3, count($applicants)); $i++): ?>
                                                <th>Applicant <?= $i + 1 ?></th>
                                            <?php endfor; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Qualification</td>
                                            <?php for ($i = 0; $i < min(3, count($applicants)); $i++): ?>
                                                <td><?= esc($applicants[$i]['rate_qualification']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td>Experience</td>
                                            <?php for ($i = 0; $i < min(3, count($applicants)); $i++): ?>
                                                <td><?= esc($applicants[$i]['rate_experience']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td>Training</td>
                                            <?php for ($i = 0; $i < min(3, count($applicants)); $i++): ?>
                                                <td><?= esc($applicants[$i]['rate_trainings']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td>Skills/Competencies</td>
                                            <?php for ($i = 0; $i < min(3, count($applicants)); $i++): ?>
                                                <td><?= esc($applicants[$i]['rate_skills_competencies']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td>Knowledge</td>
                                            <?php for ($i = 0; $i < min(3, count($applicants)); $i++): ?>
                                                <td><?= esc($applicants[$i]['rate_knowledge']) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                        <tr>
                                            <td>Weighting Out of 25</td>
                                            <?php for ($i = 0; $i < min(3, count($applicants)); $i++): ?>
                                                <?php
                                                $weighting = $applicants[$i]['rate_qualification'] +
                                                             $applicants[$i]['rate_experience'] +
                                                             $applicants[$i]['rate_trainings'] +
                                                             $applicants[$i]['rate_skills_competencies'] +
                                                             $applicants[$i]['rate_knowledge'];
                                                ?>
                                                <td><?= number_format($weighting, 2) ?></td>
                                            <?php endfor; ?>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-6">
                                <h5>SHORT LIST IN ORDER OF PREFERENCE</h5>
                                <ol>
                                    <?php 
                                    usort($applicants, function($a, $b) {
                                        $weightingA = $a['rate_qualification'] + $a['rate_experience'] + $a['rate_trainings'] + $a['rate_skills_competencies'] + $a['rate_knowledge'];
                                        $weightingB = $b['rate_qualification'] + $b['rate_experience'] + $b['rate_trainings'] + $b['rate_skills_competencies'] + $b['rate_knowledge'];
                                        return $weightingB <=> $weightingA;
                                    });
                                    foreach (array_slice($applicants, 0, 3) as $applicant): 
                                    ?>
                                        <li><?= esc($applicant['name']) ?></li>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                            <div class="col-6">
                                <h5>ELIMINATED</h5>
                                <ol>
                                    <?php foreach ($applicants as $applicant): ?>
                                        <?php if ($applicant['application_status'] === 'Eliminated'): ?>
                                            <li><?= esc($applicant['name']) ?></li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                        </div>

                        <!-- Pagination buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <nav aria-label="Applicant navigation">
                                    <ul class="pagination justify-content-center">
                                        <?php if ($currentPage > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?= $currentPage - 1 ?>">Previous</a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?= ($i == $currentPage) ? 'active' : '' ?>">
                                                <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php if ($currentPage < $totalPages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?= $currentPage + 1 ?>">Next</a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        </div>

                        <div class="row no-print mt-4">
                            <div class="col-12">
                                <a href="javascript:window.print();" class="btn btn-default"><i class="fas fa-print"></i> Print</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
