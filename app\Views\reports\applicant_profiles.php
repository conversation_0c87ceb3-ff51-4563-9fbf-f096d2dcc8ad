<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports/viewPositions/' . $position['position_group_id']) ?>">View Positions</a></li>
                        <li class="breadcrumb-item active">Applicant Profiles</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="invoice p-3 mb-3">
                        <div class="row">
                            <div class="col-12 text-center">
                                <!-- <img src="<?= imgcheck($org['org_logo']) ?>" alt="Papua New Guinea Coat of Arms" style="width: 100px;"> -->
                                <img src="<?= base_url('public/assets/system_img/png_emblem.jpg') ?>" alt="Papua New Guinea Coat of Arms" style="width: 100px;">
                                <h4 class="mt-3">PUBLIC SERVICE OF PAPUA NEW GUINEA</h4>
                                <h5>SHORT LIST APPLICANT PROFILE</h5>
                                <p class="float-right">FORM RS 3.7</p>
                            </div>
                        </div>
                        <div class="row invoice-info mt-4">
                            <div class="col-sm-6 invoice-col">
                                <b>Advertisement No:</b> <span><?= esc($org['advertisement_no']) ?></span><br>
                                <b>Advertisement Date:</b> <span><?= esc($org['advertisement_date']) ?></span><br>
                                <b>Mode of Advert:</b> <span><?= esc($org['mode_of_advert']) ?></span>
                                <br>
                                <b>Total No. Of Applicants:</b> <span><?= count($applicants) ?></span>

                            </div>
                            <div class="col-sm-6 invoice-col">
                                <b>Position No:</b> <span><?= esc($position['position_no']) ?></span><br>
                                <b>Designation:</b> <span><?= esc($position['designation']) ?></span><br>
                                <b>Classification:</b> <span><?= esc($position['classification']) ?></span>
                            </div>

                        </div>

                        <?php
                        $totalApplicants = count($applicants);
                        $applicantsPerPage = 3;
                        $totalPages = ceil($totalApplicants / $applicantsPerPage);
                        ?>

                        <?php for ($page = 1; $page <= $totalPages; $page++): ?>
                            <?php
                            $startIndex = ($page - 1) * $applicantsPerPage;
                            $pageApplicants = array_slice($applicants, $startIndex, $applicantsPerPage);
                            ?>

                            <div class="page-section" id="page-<?= $page ?>" style="<?= $page > 1 ? 'display: none;' : '' ?>">
                                <div class="row mt-4">
                                    <div class="col-12 table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr class="">
                                                    <th>POSITION SPECIFICATION</th>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <th>PERSON SPECIFICATION<br>Applicant <?= $startIndex + $i + 1 ?></th>
                                                    <?php endfor; ?>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td>
                                                        <b>Name:</b>   <?= esc($pageApplicants[$i]['name']) ?><br>
                                                        <b>Sex:</b>    <?= esc($pageApplicants[$i]['sex']) ?><br>
                                                        <b>Age:</b>    <?= esc($pageApplicants[$i]['age']) ?><br>
                                                        <b>Current Position:</b> <?= esc($pageApplicants[$i]['current_position']) ?><br>
                                                        <b>Address/Location:</b> <?= esc($pageApplicants[$i]['address_location']) ?><br>

                                                        </td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <tr class="">
                                                    <td>Qualifications (abstract from
                                                    JD):</td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <tr>
                                                    <td><strong><?= esc($position['qualifications']) ?></strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['qualification_text'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr class="">
                                                    <td><strong>Other training/courses attended</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>

                                                <tr>
                                                    <td> </td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['other_trainings'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr class="">
                                                    <td><strong>Knowledge:</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td><strong><?= esc($position['knowledge']) ?></strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['knowledge'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr class="">
                                                    <td><strong>Skills/Competencies</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td><strong><?= esc($position['skills_competencies']) ?></strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['skills_competencies'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr class="">
                                                    <td><strong>Related Job Experience:</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td><strong><?= esc($position['job_experiences']) ?></strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['job_experiences'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr class="">
                                                    <td><strong>Comments</strong></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td></td>
                                                    <?php endfor; ?>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <td><?= nl2br(esc($pageApplicants[$i]['comments'])) ?></td>
                                                    <?php endfor; ?>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-12 table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Criteria</th>
                                                    <?php for ($i = 0; $i < count($pageApplicants); $i++): ?>
                                                        <th>Applicant <?= $startIndex + $i + 1 ?></th>
                                                    <?php endfor; ?>
                                                    <th>Out of</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Age</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_age']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_age'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Qualification</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_qualification']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_qualification'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Experience</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_private_non_relevant'] + $applicant['rate_private_relevant'] + $applicant['rate_public_non_relevant'] + $applicant['rate_public_relevant']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= ($applicant['max_rate_private_non_relevant'] ?? 0) + ($applicant['max_rate_private_relevant'] ?? 0) + ($applicant['max_rate_public_non_relevant'] ?? 0) + ($applicant['max_rate_public_relevant'] ?? 0) ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Trainings</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_trainings']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_trainings'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Skills/Competencies</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_skills_competencies']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_skills_competencies'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Knowledge</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_knowledge']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_knowledge'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Capability</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_capability']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_capability'] ?? 0 ?></td>
                                                </tr>
                                                <tr>
                                                    <td>Public Service Status</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_public_service']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_public_service'] ?? 0 ?></td>
                                                </tr>
                                                <tr class="text-bold bg-secondary bg-opacity-10">
                                                    <td>Total</td>
                                                    <?php foreach ($pageApplicants as $applicant): ?>
                                                        <td><?= esc($applicant['rate_total']) ?></td>
                                                    <?php endforeach; ?>
                                                    <td><?= $applicant['max_rate_total'] ?? 0 ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>

                        <div class="row mt-4">
                            <div class="col-6">
                                <h5>SHORT LIST IN ORDER OF PREFERENCE</h5>
                                <ol>
                                    <?php foreach ($applicants as $applicant): ?>
                                        <?php if ($applicant['application_status'] === 'Shortlisted'): ?>
                                            <li><?= esc($applicant['name']) ?> (<?= esc($applicant['rate_total']) ?>)</li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                            <div class="col-6">
                                <h5>ELIMINATED</h5>
                                <ol>
                                    <?php foreach ($applicants as $applicant): ?>
                                        <?php if ($applicant['application_status'] === 'Eliminated'): ?>
                                            <li><?= esc($applicant['name']) ?> - <em><?= esc($applicant['app_status_reason']) ?></em> </li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                        </div>

                        <!-- Client-side pagination buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <nav aria-label="Applicant navigation">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item" id="prev-btn">
                                            <a class="page-link" href="javascript:void(0)">Previous</a>
                                        </li>

                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?= ($i == 1) ? 'active' : '' ?>" data-page="<?= $i ?>">
                                                <a class="page-link" href="javascript:void(0)"><?= $i ?></a>
                                            </li>
                                        <?php endfor; ?>

                                        <li class="page-item" id="next-btn">
                                            <a class="page-link" href="javascript:void(0)">Next</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>

                        <div class="row no-print mt-4">
                            <div class="col-12">
                                <a href="javascript:void(0);" id="print-btn" class="btn btn-default"><i class="fas fa-print"></i> Print</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Client-side pagination
    const totalPages = <?= $totalPages ?>;
    let currentPage = 1;

    // Function to show the selected page and hide others
    function showPage(pageNum) {
        // Hide all pages
        document.querySelectorAll('.page-section').forEach(page => {
            page.style.display = 'none';
        });

        // Show the selected page
        const selectedPage = document.getElementById('page-' + pageNum);
        if (selectedPage) {
            selectedPage.style.display = 'block';
        }

        // Update active class on pagination buttons
        document.querySelectorAll('.pagination .page-item').forEach(item => {
            if (item.dataset.page == pageNum) {
                item.classList.add('active');
            } else if (item.dataset.page) {
                item.classList.remove('active');
            }
        });

        // Update current page
        currentPage = pageNum;

        // Update previous/next button states
        document.getElementById('prev-btn').classList.toggle('disabled', currentPage <= 1);
        document.getElementById('next-btn').classList.toggle('disabled', currentPage >= totalPages);
    }

    // Add click handlers to page buttons
    document.querySelectorAll('.pagination .page-item[data-page]').forEach(item => {
        item.addEventListener('click', function() {
            showPage(parseInt(this.dataset.page));
        });
    });

    // Add click handlers to previous/next buttons
    document.getElementById('prev-btn').addEventListener('click', function() {
        if (currentPage > 1) {
            showPage(currentPage - 1);
        }
    });

    document.getElementById('next-btn').addEventListener('click', function() {
        if (currentPage < totalPages) {
            showPage(currentPage + 1);
        }
    });

    // Print functionality - show all pages
    document.getElementById('print-btn').addEventListener('click', function() {
        // Save current state
        const previouslyVisiblePage = currentPage;

        // Add print-specific styles
        const style = document.createElement('style');
        style.id = 'print-style';
        style.innerHTML = `
            @media print {
                .page-section {
                    display: block !important;
                    page-break-after: always;
                }
                .pagination, .no-print {
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(style);

        // Show all pages for printing
        document.querySelectorAll('.page-section').forEach(page => {
            page.style.display = 'block';
        });

        // Print
        window.print();

        // Remove print styles and restore previous state after print dialog closes
        setTimeout(function() {
            document.head.removeChild(style);
            showPage(previouslyVisiblePage);
        }, 1000);
    });

    // Initialize the first page
    showPage(1);
});
</script>
<?= $this->endSection() ?>