<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Applicants Analysis for <?= esc($position['designation']) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("reports/viewPositions/{$position['position_group_id']}") ?>">View Positions</a></li>
                        <li class="breadcrumb-item active">Applicants Analysis</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list mr-2"></i>
                        Position: <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                    </h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Rank</th>
                                    <th>Name</th>
                                    <th>Age</th>
                                    <th>Qualification</th>
                                    <th>Experience</th>
                                    <th>Trainings</th>
                                    <th>Skills</th>
                                    <th>Knowledge</th>
                                    <th>Capability</th>
                                    <th>PS Status</th>
                                    <th>Total Score</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $x=1; foreach ($applicants as $applicant): ?>
                                    <?php 
                                        $totalScore = 
                                            ($applicant['rate_age'] ?? 0) +
                                            ($applicant['rate_qualification'] ?? 0) +
                                            ($applicant['rate_trainings'] ?? 0) +
                                            ($applicant['rate_skills_competencies'] ?? 0) +
                                            ($applicant['rate_knowledge'] ?? 0) +
                                            ($applicant['rate_public_service'] ?? 0) +
                                            ($applicant['rate_private_non_relevant'] ?? 0) +
                                            ($applicant['rate_private_relevant'] ?? 0) +
                                            ($applicant['rate_public_non_relevant'] ?? 0) +
                                            ($applicant['rate_public_relevant'] ?? 0) +
                                            ($applicant['rate_capability'] ?? 0);

                                        $experienceScore = 
                                            ($applicant['rate_private_non_relevant'] ?? 0) +
                                            ($applicant['rate_private_relevant'] ?? 0) +
                                            ($applicant['rate_public_non_relevant'] ?? 0) +
                                            ($applicant['rate_public_relevant'] ?? 0);
                                    ?>
                                    <tr>
                                        <td><?= $x++ ?></td>
                                        <td><?= esc($applicant['name']) ?></td>
                                        <td><?= esc($applicant['rate_age'] ?? 0) ?>/<?= esc($applicant['max_rate_age'] ?? 0) ?></td>
                                        <td><?= esc($applicant['rate_qualification'] ?? 0) ?>/<?= esc($applicant['max_rate_qualification'] ?? 0) ?></td>
                                        <td><?= esc($experienceScore) ?>/<?= esc(($applicant['max_rate_private_non_relevant'] ?? 0) + ($applicant['max_rate_private_relevant'] ?? 0) + ($applicant['max_rate_public_non_relevant'] ?? 0) + ($applicant['max_rate_public_relevant'] ?? 0)) ?></td>
                                        <td><?= esc($applicant['rate_trainings'] ?? 0) ?>/<?= esc($applicant['max_rate_trainings'] ?? 0) ?></td>
                                        <td><?= esc($applicant['rate_skills_competencies'] ?? 0) ?>/<?= esc($applicant['max_rate_skills_competencies'] ?? 0) ?></td>
                                        <td><?= esc($applicant['rate_knowledge'] ?? 0) ?>/<?= esc($applicant['max_rate_knowledge'] ?? 0) ?></td>
                                        <td><?= esc($applicant['rate_capability'] ?? 0) ?>/<?= esc($applicant['max_rate_capability'] ?? 0) ?></td>
                                        <td><?= esc($applicant['rate_public_service'] ?? 0) ?>/<?= esc($applicant['max_rate_public_service'] ?? 0) ?></td>
                                        <td class="font-weight-bold"><?= esc($totalScore) ?></td>
                                        <td>
                                            <a href="<?= base_url("reports/viewApplicantAnalysis/{$applicant['id']}") ?>" class="btn btn-info btn-sm">
                                                <i class="fas fa-chart-bar"></i> Analysis
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?> 