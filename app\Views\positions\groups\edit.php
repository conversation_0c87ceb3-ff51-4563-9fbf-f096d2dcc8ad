<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('positions') ?>">Position Groups</a></li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-edit mr-2"></i>
                                Edit Position Group
                            </h3>
                        </div>
                        <?= form_open('positions/update/' . $group['id']) ?>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="group_name">
                                    <i class="fas fa-tag mr-1"></i>
                                    Group Name
                                </label>
                                <input type="text" class="form-control" id="group_name" name="group_name" value="<?= esc($group['group_name']) ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="priority">
                                    <i class="fas fa-sort-numeric-down mr-1"></i>
                                    Priority
                                </label>
                                <input type="number" class="form-control" id="priority" name="priority" value="<?= esc($group['priority']) ?>" required>
                                <small class="form-text text-muted">Lower number means higher priority</small>
                            </div>
                            <div class="form-group">
                                <label for="description">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Description
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3" required><?= esc($group['description']) ?></textarea>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i>
                                Update Group
                            </button>
                            <a href="<?= base_url('positions') ?>" class="btn btn-secondary">
                                <i class="fas fa-times mr-1"></i>
                                Cancel
                            </a>
                        </div>
                        <?= form_close() ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
