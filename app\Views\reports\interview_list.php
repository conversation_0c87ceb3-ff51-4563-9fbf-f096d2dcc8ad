<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Interview List for <?= esc($position['designation']) ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item active">Interview List</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Shortlisted Applicants for <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?></h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($applicants)): ?>
                                <div class="alert alert-info">No shortlisted applicants found for this position.</div>
                            <?php else: ?>
                                <table id="interview-table" class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Applicant Name</th>
                                            <th>Position</th>
                                            <th>Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($applicants as $applicant): ?>
                                        <tr>
                                            <td></td> <!-- Date column left blank -->
                                            <td></td> <!-- Time column left blank -->
                                            <td><?= esc($applicant['name']) ?></td>
                                            <td><?= esc($position['designation']) ?></td>
                                            <td></td> <!-- Remarks column left blank -->
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#interview-table').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "dom": 'Bfrtip',
        "buttons": [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Export to Excel',
                className: 'btn btn-success',
                title: '<?= esc($position['designation']) ?> - Interview List',
                exportOptions: {
                    columns: ':visible'
                }
            },
            {
                extend: 'colvis',
                text: '<i class="fas fa-columns"></i> Column visibility',
                className: 'btn btn-info'
            }
        ],
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 