<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-door-open mr-2"></i>
                        Open Interview
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item active">Open Interview</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position Information
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/questions/' . $position['id']) ?>" class="btn btn-info btn-sm mr-2">
                            <i class="fas fa-question-circle mr-1"></i>
                            Interview Questions
                            <span class="badge badge-light ml-1"><?= $questionsCount ?></span>
                        </a>
                        <a href="<?= base_url('interviews/interviewers/' . $position['id']) ?>" class="btn btn-warning btn-sm mr-2">
                            <i class="fas fa-users mr-1"></i>
                            Interviewers
                            <span class="badge badge-light ml-1"><?= $interviewersCount ?></span>
                        </a>
                        <a href="<?= base_url('interviews') ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Interviews
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="30%"><strong>Position No:</strong></td>
                                    <td><?= esc($position['position_no']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Designation:</strong></td>
                                    <td><?= esc($position['designation']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Classification:</strong></td>
                                    <td>
                                        <span class="badge badge-secondary">
                                            <?= esc($position['classification']) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Award:</strong></td>
                                    <td><?= esc($position['award']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="30%"><strong>Position Group:</strong></td>
                                    <td><?= esc($positionGroup['group_name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Priority:</strong></td>
                                    <td>
                                        <span class="badge badge-info">
                                            <?= $positionGroup['priority'] ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Shortlisted:</strong></td>
                                    <td>
                                        <span class="badge badge-success badge-lg">
                                            <?= count($applicants) ?> 
                                            <?= count($applicants) > 1 ? 'Applicants' : 'Applicant' ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-warning">
                                            Ready for Interview
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($position['qualifications'])): ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><strong>Qualifications:</strong></h6>
                                <div class="alert alert-light">
                                    <?= nl2br(esc($position['qualifications'])) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Shortlisted Applicants Card -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users mr-2"></i>
                        Shortlisted Applicants for Interview
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-success">
                            <?= count($applicants) ?> <?= count($applicants) > 1 ? 'Applicants' : 'Applicant' ?>
                        </span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($applicants)): ?>
                        <div class="alert alert-warning m-3">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> No Shortlisted Applicants</h5>
                            <p>There are no shortlisted applicants for this position. Please shortlist applicants first before conducting interviews.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="applicantsTable">
                                <thead class="thead-light">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="25%">Name</th>
                                        <th width="20%">Contact Details</th>
                                        <th width="15%">Application Date</th>
                                        <th width="10%" class="text-center">Status</th>
                                        <th width="15%" class="text-center">Last Updated</th>
                                        <th width="10%" class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applicants as $index => $applicant): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <strong><?= esc($applicant['name']) ?></strong>
                                            </td>
                                            <td>
                                                <small><?= esc($applicant['contact_details']) ?></small>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($applicant['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge badge-success">
                                                    <?= esc($applicant['application_status']) ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($applicant['updated_at'])) ?>
                                                </small>
                                            </td>
                                            <td class="text-center">
                                                <a href="<?= base_url('interviews/data/view/' . $applicant['id']) ?>"
                                                   class="btn btn-success btn-sm">
                                                    <i class="fas fa-clipboard-list mr-1"></i>
                                                    View Interview Data
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Interview Actions Card -->
            <div class="card card-warning card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs mr-2"></i>
                        Interview Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box <?= $questionsCount > 0 ? 'bg-success' : 'bg-warning' ?>">
                                <span class="info-box-icon"><i class="fas fa-question-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Interview Questions</span>
                                    <span class="info-box-number"><?= $questionsCount ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: <?= $questionsCount > 0 ? '100' : '0' ?>%"></div>
                                    </div>
                                    <span class="progress-description">
                                        <?= $questionsCount > 0 ? 'Ready' : 'Not Set' ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box <?= $interviewersCount >= 2 ? 'bg-success' : ($interviewersCount > 0 ? 'bg-warning' : 'bg-danger') ?>">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Interviewers</span>
                                    <span class="info-box-number"><?= $interviewersCount ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: <?= min(($interviewersCount / 3) * 100, 100) ?>%"></div>
                                    </div>
                                    <span class="progress-description">
                                        <?= $interviewersCount >= 2 ? 'Panel Ready' : ($interviewersCount > 0 ? 'Incomplete' : 'Not Set') ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box <?= ($questionsCount > 0 && $interviewersCount >= 2) ? 'bg-success' : 'bg-secondary' ?>">
                                <span class="info-box-icon"><i class="fas fa-calendar-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Schedule Interviews</span>
                                    <span class="info-box-number"><?= ($questionsCount > 0 && $interviewersCount >= 2) ? 'Ready' : 'Pending' ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: <?= ($questionsCount > 0 && $interviewersCount >= 2) ? '100' : '0' ?>%"></div>
                                    </div>
                                    <span class="progress-description">
                                        <?= ($questionsCount > 0 && $interviewersCount >= 2) ? 'Available' : 'Setup Required' ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-clipboard-list"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Interview Forms</span>
                                    <span class="info-box-number"><?= ($questionsCount > 0 && $interviewersCount >= 2) ? 'Ready' : 'Pending' ?></span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: <?= ($questionsCount > 0 && $interviewersCount >= 2) ? '100' : '50' ?>%"></div>
                                    </div>
                                    <span class="progress-description">
                                        Generate Forms
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <?php if ($questionsCount == 0 && $interviewersCount == 0): ?>
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle mr-2"></i>Setup Required:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Add Interview Questions:</strong> Click the "Interview Questions" button to create questions for this position</li>
                                        <li><strong>Add Interviewers:</strong> Click the "Interviewers" button to set up the interview panel</li>
                                        <li>Minimum 2-3 interviewers recommended for a complete panel</li>
                                        <li>At least 5-10 questions recommended for thorough evaluation</li>
                                    </ul>
                                </div>
                            <?php elseif ($questionsCount == 0): ?>
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-question-circle mr-2"></i>Questions Needed:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Add Interview Questions:</strong> Click the "Interview Questions" button to create questions</li>
                                        <li>You have <?= $interviewersCount ?> interviewer(s) ready</li>
                                        <li>Recommended: 5-10 questions for comprehensive evaluation</li>
                                    </ul>
                                </div>
                            <?php elseif ($interviewersCount < 2): ?>
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-users mr-2"></i>More Interviewers Needed:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Add More Interviewers:</strong> Click the "Interviewers" button to add panel members</li>
                                        <li>You have <?= $questionsCount ?> question(s) ready</li>
                                        <li>Current panel: <?= $interviewersCount ?> interviewer(s) - Recommended: 2-3 interviewers</li>
                                    </ul>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle mr-2"></i>Interview Setup Complete - Next Steps:</h6>
                                    <ul class="mb-0">
                                        <li><strong>✓ Questions Ready:</strong> <?= $questionsCount ?> interview questions prepared</li>
                                        <li><strong>✓ Panel Ready:</strong> <?= $interviewersCount ?> interviewers assigned</li>
                                        <li><strong>Next:</strong> Schedule interview dates and times</li>
                                        <li><strong>Then:</strong> Send interview notifications to <?= count($applicants) ?> shortlisted applicants</li>
                                        <li><strong>Finally:</strong> Conduct interviews and record results</li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable for applicants
    $('#applicantsTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": true,
        "pageLength": 10,
        "order": [[ 1, "asc" ]], // Sort by name
        "columnDefs": [
            { "orderable": false, "targets": [6] } // Disable sorting for action column
        ]
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
<?= $this->endSection() ?>
