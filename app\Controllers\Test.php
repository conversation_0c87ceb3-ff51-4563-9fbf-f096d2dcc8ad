<?php

namespace App\Controllers;

use CodeIgniter\HTTP\Response;
use App\Models\basefilesModel;
use App\Models\countryModel;
use App\Models\districtModel;
use App\Models\eventsModel;
use App\Models\provinceModel;
use App\Models\roadsModel;
use App\Models\usersModel;
use Ol\format\KML;

class Test extends BaseController
{
    public $session;
    public $usersModel;
    public $eventsModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    public function index()
    {
        
    }

}
